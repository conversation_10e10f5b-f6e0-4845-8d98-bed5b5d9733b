<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Attendance Report Wizard Form View -->
    <record id="view_attendance_report_wizard_form" model="ir.ui.view">
        <field name="name">biometric.attendance.report.wizard.form</field>
        <field name="model">biometric.attendance.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Attendance Report Wizard">
                <sheet>
                    <group>
                        <group name="date_range">
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="report_type"/>
                        </group>
                        <group name="filters">
                            <field name="employee_ids" widget="many2many_tags"/>
                            <field name="device_ids" widget="many2many_tags"/>
                            <field name="department"/>
                        </group>
                    </group>
                    
                    <group name="options" string="Options">
                        <group>
                            <field name="include_weekends"/>
                            <field name="include_holidays"/>
                        </group>
                        <group>
                            <field name="group_by_department"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button string="Generate Report" name="action_generate_report" type="object" class="btn-primary"/>
                    <button string="Export Excel" name="action_export_excel" type="object" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Attendance Report Wizard Action -->
    <record id="action_attendance_report_wizard" model="ir.actions.act_window">
        <field name="name">Generate Attendance Report</field>
        <field name="res_model">biometric.attendance.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Sync Wizard Form View -->
    <record id="view_biometric_sync_wizard_form" model="ir.ui.view">
        <field name="name">biometric.sync.wizard.form</field>
        <field name="model">biometric.sync.wizard</field>
        <field name="arch" type="xml">
            <form string="Device Synchronization Wizard">
                <sheet>
                    <group>
                        <group name="devices">
                            <field name="device_ids" widget="many2many_tags" required="1"/>
                            <field name="sync_type"/>
                        </group>
                        <group name="date_range" attrs="{'invisible': [('sync_type', '=', 'employees')]}">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                    
                    <group name="options" string="Options">
                        <group>
                            <field name="force_sync"/>
                            <field name="test_connection"/>
                        </group>
                    </group>
                    
                    <group name="results" string="Results" attrs="{'invisible': [('sync_results', '=', False)]}">
                        <field name="sync_results" nolabel="1" readonly="1"/>
                    </group>
                </sheet>
                <footer>
                    <button string="Test Connections" name="action_test_connections" type="object" class="btn-secondary"/>
                    <button string="Start Sync" name="action_sync_devices" type="object" class="btn-primary"/>
                    <button string="Schedule Sync" name="action_schedule_sync" type="object" class="btn-info"/>
                    <button string="Clear Data" name="action_clear_device_data" type="object" class="btn-warning" confirm="This will delete all data from selected devices. Are you sure?"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Sync Wizard Action -->
    <record id="action_biometric_sync_wizard" model="ir.actions.act_window">
        <field name="name">Device Synchronization</field>
        <field name="res_model">biometric.sync.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Add menu items for wizards -->
    <menuitem id="menu_biometric_report_wizard" 
              name="Report Wizard" 
              parent="menu_biometric_reports" 
              action="action_attendance_report_wizard" 
              sequence="90"/>

    <menuitem id="menu_biometric_sync_wizard" 
              name="Sync Wizard" 
              parent="menu_biometric_devices" 
              action="action_biometric_sync_wizard" 
              sequence="20"/>
</odoo>
