# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Користувач проксі облікового запису EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr "Проксі клієнт облікового запису Edi"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "Активно"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_format_code
msgid "Code"
msgstr "Код"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "Компанія"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "Створив"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "Створено"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "Відобразити назву"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_format
msgid "EDI format"
msgstr "Формат EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_format_id
msgid "Edi Format"
msgstr "Формат Edi"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr "Ідентифікація Edi "

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ID"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr "Id клієнт"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user____last_update
msgid "Last Modified on"
msgstr "Останні зміни"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "Private Key"
msgstr "Приватний ключ"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "Оновити токен"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "The key to encrypt all the user's data"
msgstr "Ключ для шифрування всіх даних користувача"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid ""
"The unique id that identifies this user for on the edi format, typically the"
" vat"
msgstr ""
"Унікальний ідентифікатор, який ідентифікує цього користувача у форматі edi, "
"як правило, пдв"

#. module: account_edi_proxy_client
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr ""
"URL-адреса, яку запитала ця служба, повернула помилку. URL-адреса, з якою "
"вона намагалася зв’язатися, була %s"

#. module: account_edi_proxy_client
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s. %s"
msgstr ""
"URL-адреса, яку запитала ця служба, повернула помилку. URL-адреса, з якою "
"вона намагалася зв’язатися, була %s. %s"

#. module: account_edi_proxy_client
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service tried to contact does not exist. The url was %r"
msgstr ""
"URL-адреса, з якою намагалася зв’язатися ця служба, не існує. URL-адреса "
"була"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_edi_identification_per_format
msgid "This edi identification is already assigned to a user"
msgstr "Цей ідентифікатор edi вже призначено користувачеві"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr "Цей id_client вже використовує інший користувач."
