<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Attendance Tree View -->
    <record id="view_biometric_attendance_tree" model="ir.ui.view">
        <field name="name">biometric.attendance.tree</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <tree string="Attendance Records" decoration-success="status=='present'" decoration-warning="status=='late'" decoration-danger="status=='absent'" decoration-info="status=='holiday'">
                <field name="attendance_date"/>
                <field name="employee_id"/>
                <field name="device_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="worked_hours" sum="Total Hours"/>
                <field name="late_minutes" sum="Total Late Minutes"/>
                <field name="overtime_hours" sum="Total Overtime"/>
                <field name="status"/>
                <field name="manual_entry"/>
            </tree>
        </field>
    </record>

    <!-- Biometric Attendance Form View -->
    <record id="view_biometric_attendance_form" model="ir.ui.view">
        <field name="name">biometric.attendance.form</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <form string="Attendance Record">
                <header>
                    <button name="action_manual_checkout" string="Manual Check-out" type="object" class="btn-primary" attrs="{'invisible': ['|', ('check_in', '=', False), ('check_out', '!=', False)]}"/>
                    <field name="status" widget="statusbar" statusbar_visible="present,late,absent,holiday"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_employee" type="object" class="oe_stat_button" icon="fa-user">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Employee</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="employee_id" options="{'no_create': True}"/>
                        </h1>
                        <h2>
                            <field name="attendance_date"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="device_id" options="{'no_create': True}"/>
                            <field name="manual_entry"/>
                            <field name="is_holiday"/>
                        </group>
                        <group name="time_info">
                            <field name="check_in"/>
                            <field name="check_out"/>
                            <field name="worked_hours"/>
                        </group>
                    </group>
                    
                    <group name="calculations" string="Calculations">
                        <group>
                            <field name="late_minutes"/>
                            <field name="is_late"/>
                        </group>
                        <group>
                            <field name="early_departure_minutes"/>
                            <field name="early_departure"/>
                        </group>
                        <group>
                            <field name="overtime_hours"/>
                        </group>
                        <group>
                            <field name="is_absent"/>
                        </group>
                    </group>
                    
                    <group name="notes" string="Notes">
                        <field name="notes" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Biometric Attendance Calendar View -->
    <record id="view_biometric_attendance_calendar" model="ir.ui.view">
        <field name="name">biometric.attendance.calendar</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <calendar string="Attendance Calendar" date_start="attendance_date" color="employee_id" mode="month">
                <field name="employee_id"/>
                <field name="status"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="worked_hours"/>
            </calendar>
        </field>
    </record>

    <!-- Biometric Attendance Pivot View -->
    <record id="view_biometric_attendance_pivot" model="ir.ui.view">
        <field name="name">biometric.attendance.pivot</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <pivot string="Attendance Analysis">
                <field name="attendance_date" type="row" interval="month"/>
                <field name="employee_id" type="row"/>
                <field name="worked_hours" type="measure"/>
                <field name="late_minutes" type="measure"/>
                <field name="overtime_hours" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Biometric Attendance Graph View -->
    <record id="view_biometric_attendance_graph" model="ir.ui.view">
        <field name="name">biometric.attendance.graph</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <graph string="Attendance Statistics" type="bar">
                <field name="attendance_date" interval="week"/>
                <field name="worked_hours" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Biometric Attendance Search View -->
    <record id="view_biometric_attendance_search" model="ir.ui.view">
        <field name="name">biometric.attendance.search</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <search string="Search Attendance">
                <field name="employee_id"/>
                <field name="device_id"/>
                <field name="attendance_date"/>
                <filter string="Today" name="today" domain="[('attendance_date', '=', context_today())]"/>
                <filter string="This Week" name="this_week" domain="[('attendance_date', '&gt;=', (context_today() - relativedelta(weeks=1))), ('attendance_date', '&lt;=', context_today())]"/>
                <filter string="This Month" name="this_month" domain="[('attendance_date', '&gt;=', (context_today() - relativedelta(months=1))), ('attendance_date', '&lt;=', context_today())]"/>
                <separator/>
                <filter string="Present" name="present" domain="[('status', '=', 'present')]"/>
                <filter string="Late" name="late" domain="[('status', '=', 'late')]"/>
                <filter string="Absent" name="absent" domain="[('status', '=', 'absent')]"/>
                <filter string="Early Departure" name="early_departure" domain="[('status', '=', 'early_departure')]"/>
                <filter string="Holiday" name="holiday" domain="[('status', '=', 'holiday')]"/>
                <separator/>
                <filter string="Manual Entry" name="manual_entry" domain="[('manual_entry', '=', True)]"/>
                <filter string="With Overtime" name="overtime" domain="[('overtime_hours', '&gt;', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Device" name="group_device" context="{'group_by': 'device_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'attendance_date'}"/>
                    <filter string="Month" name="group_month" context="{'group_by': 'attendance_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Biometric Attendance Action -->
    <record id="action_biometric_attendance" model="ir.actions.act_window">
        <field name="name">Attendance Records</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">tree,form,calendar,pivot,graph</field>
        <field name="search_view_id" ref="view_biometric_attendance_search"/>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No attendance records found!
            </p>
            <p>
                Attendance records are automatically synchronized from biometric devices.
                You can also create manual entries for employees.
            </p>
        </field>
    </record>

    <!-- Today's Attendance Action -->
    <record id="action_biometric_attendance_today" model="ir.actions.act_window">
        <field name="name">Today's Attendance</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="domain">[('attendance_date', '=', context_today())]</field>
        <field name="context">{'search_default_today': 1}</field>
    </record>
</odoo>
