.o_biometric_dashboard {
    padding: 20px;
    background-color: #f8f9fa;
}

.o_biometric_dashboard .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 20px;
}

.o_biometric_dashboard .card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

.o_biometric_dashboard .bg-primary {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.o_biometric_dashboard .bg-success {
    background: linear-gradient(45deg, #28a745, #1e7e34) !important;
}

.o_biometric_dashboard .bg-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800) !important;
}

.o_biometric_dashboard .bg-danger {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.o_biometric_dashboard .card-body {
    padding: 1.5rem;
}

.o_biometric_dashboard .card-title {
    margin-bottom: 0.5rem;
    font-size: 2rem;
    font-weight: 700;
}

.o_biometric_dashboard .card-text {
    margin-bottom: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.o_biometric_dashboard .border-left-primary {
    border-left: 4px solid #007bff !important;
}

.o_biometric_dashboard .btn-block {
    margin-bottom: 10px;
    padding: 12px;
    font-weight: 500;
}

.o_biometric_dashboard .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.o_biometric_dashboard .table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

.o_biometric_dashboard .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

.o_biometric_dashboard canvas {
    max-height: 300px;
}

.o_biometric_dashboard .fa-2x {
    font-size: 2em;
}

.o_biometric_dashboard .fa-lg {
    font-size: 1.33333em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .o_biometric_dashboard {
        padding: 10px;
    }
    
    .o_biometric_dashboard .card-title {
        font-size: 1.5rem;
    }
    
    .o_biometric_dashboard .btn-block {
        margin-bottom: 15px;
    }
}

/* Animation for cards */
.o_biometric_dashboard .card {
    transition: transform 0.2s ease-in-out;
}

.o_biometric_dashboard .card:hover {
    transform: translateY(-2px);
}

/* Loading spinner */
.o_biometric_dashboard .fa-spinner {
    color: #007bff;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-connected {
    background-color: #28a745;
}

.status-disconnected {
    background-color: #6c757d;
}

.status-error {
    background-color: #dc3545;
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Quick action buttons */
.quick-actions .btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Device status cards */
.device-card {
    transition: all 0.3s ease;
}

.device-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Recent activity table */
.recent-activity-table {
    font-size: 0.875rem;
}

.recent-activity-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.recent-activity-table td {
    border-bottom: 1px solid #dee2e6;
}

/* Summary cards hover effects */
.summary-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.summary-card:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}
