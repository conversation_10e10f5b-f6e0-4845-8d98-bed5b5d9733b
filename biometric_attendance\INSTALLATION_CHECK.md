# Installation Checklist - Biometric Attendance Module

## Pre-Installation Requirements ✅

### System Requirements
- [ ] Odoo 15.0 or higher installed
- [ ] Python 3.6+ (recommended 3.8+)
- [ ] Network access to biometric devices (if using TCP/IP)
- [ ] Serial port access (if using serial devices)

### Dependencies
- [ ] Install pyserial: `pip install pyserial`
- [ ] Ensure Chart.js is accessible (included via CDN in module)

## Installation Steps ✅

### 1. Module Deployment
- [ ] Copy `biometric_attendance` folder to Odoo addons directory
- [ ] Verify all files are present (see file structure below)
- [ ] Check file permissions (readable by Odoo user)

### 2. Odoo Configuration
- [ ] Restart Odoo server
- [ ] Update Apps List (Apps > Update Apps List)
- [ ] Search for "Biometric Attendance System"
- [ ] Click Install

### 3. Post-Installation Verification
- [ ] No installation errors in Odoo log
- [ ] Module appears in installed apps
- [ ] Menu "Biometric Attendance" is visible
- [ ] Default work rules are created

## File Structure Verification ✅

```
biometric_attendance/
├── __init__.py
├── __manifest__.py
├── README.md
├── CHANGELOG.md
├── LICENSE
├── BUGFIX.md
├── BUGFIX_FSTRINGS.md
├── INSTALLATION_CHECK.md
├── controllers/
│   ├── __init__.py
│   └── main.py
├── data/
│   ├── biometric_data.xml
│   └── ir_cron.xml
├── demo/
│   └── biometric_demo.xml
├── lib/
│   ├── __init__.py
│   ├── device_connector.py
│   ├── zkteco_connector.py
│   └── data_parser.py
├── models/
│   ├── __init__.py
│   ├── biometric_device.py
│   ├── biometric_employee.py
│   ├── biometric_attendance.py
│   ├── biometric_work_rule.py
│   ├── biometric_config.py
│   └── res_config_settings.py
├── reports/
│   ├── attendance_report.xml
│   ├── late_report.xml
│   └── absence_report.xml
├── security/
│   ├── ir.model.access.csv
│   └── biometric_security.xml
├── static/
│   ├── description/
│   │   ├── icon.svg
│   │   └── index.html
│   └── src/
│       ├── css/
│       │   └── dashboard.css
│       ├── js/
│       │   └── dashboard.js
│       └── xml/
│           └── dashboard.xml
├── views/
│   ├── assets.xml
│   ├── biometric_device_views.xml
│   ├── biometric_employee_views.xml
│   ├── biometric_attendance_views.xml
│   ├── biometric_work_rule_views.xml
│   ├── biometric_config_views.xml
│   ├── biometric_report_views.xml
│   ├── biometric_report_wizard_views.xml
│   ├── biometric_menu.xml
│   └── res_config_settings_views.xml
└── wizards/
    ├── __init__.py
    ├── attendance_report_wizard.py
    └── sync_wizard.py
```

## Known Issues Fixed ✅

### 1. Work Rule Validation Error ✅
- **Issue**: Night shift validation error
- **Fix**: Updated validation logic to support night shifts
- **Status**: ✅ Fixed

### 2. F-String Compatibility ✅
- **Issue**: ParseError due to f-string usage in XML
- **Fix**: Replaced all f-strings with % formatting
- **Status**: ✅ Fixed

## Initial Configuration Steps ✅

### 1. Access the Module
- [ ] Go to Apps > Biometric Attendance
- [ ] Verify dashboard loads without errors

### 2. Create Work Rules
- [ ] Navigate to Configuration > Work Rules
- [ ] Verify default rules exist:
  - Standard Work Rule (8 Hours)
  - Flexible Work Rule (7 Hours)  
  - Evening Shift Rule

### 3. Configure Devices
- [ ] Go to Devices > Biometric Devices
- [ ] Create test device configuration
- [ ] Test connection (should handle gracefully if device not available)

### 4. Check Security Groups
- [ ] Go to Settings > Users & Companies > Groups
- [ ] Verify biometric groups exist:
  - Biometric User
  - Biometric Officer
  - Biometric Manager

### 5. Test Basic Functionality
- [ ] Create test employee (manually)
- [ ] Create test attendance record
- [ ] Generate test report
- [ ] Check dashboard displays data

## Troubleshooting ✅

### Installation Fails
1. Check Odoo logs for specific errors
2. Verify all dependencies installed
3. Ensure proper file permissions
4. Try installing in safe mode

### Module Not Visible
1. Restart Odoo server
2. Update apps list
3. Check module is in correct addons path
4. Verify __manifest__.py is valid

### Permission Errors
1. Check user has proper groups assigned
2. Verify security rules are loaded
3. Test with admin user first

### Device Connection Issues
1. Verify network connectivity
2. Check device IP and port settings
3. Test with device manufacturer tools first
4. Review device documentation

## Success Indicators ✅

After successful installation, you should see:
- [ ] Biometric Attendance menu in main navigation
- [ ] Dashboard with sample data (if demo data installed)
- [ ] All views load without errors
- [ ] Reports generate successfully
- [ ] Settings page accessible
- [ ] No errors in Odoo logs

## Next Steps After Installation ✅

1. **Configure Real Devices**: Set up actual biometric device connections
2. **Import Employees**: Sync employee data from devices
3. **Set Work Rules**: Configure appropriate work schedules
4. **Train Users**: Provide training on system usage
5. **Test Thoroughly**: Verify all functionality with real data
6. **Schedule Backups**: Set up regular data backups
7. **Monitor Performance**: Watch system performance with real usage

## Support Resources ✅

- **Documentation**: README.md and CHANGELOG.md
- **Bug Reports**: Check BUGFIX*.md files
- **Configuration**: Use built-in help text and tooltips
- **API**: See controllers/main.py for API endpoints

---

**Installation Date**: ___________
**Installed By**: ___________
**Odoo Version**: ___________
**Module Version**: 1.0.0
