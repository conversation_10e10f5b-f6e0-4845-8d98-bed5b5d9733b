<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Configuration Form View -->
    <record id="view_biometric_config_settings" model="ir.ui.view">
        <field name="name">biometric.config.settings.form</field>
        <field name="model">biometric.config.settings</field>
        <field name="arch" type="xml">
            <form string="Biometric Attendance Settings" class="oe_form_configuration">
                <header>
                    <button string="Save" type="object" name="execute" class="oe_highlight"/>
                    <button string="Cancel" type="object" name="cancel" class="oe_link"/>
                </header>
                <sheet>
                    <div class="o_setting_container">
                        <div class="o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="auto_sync_enabled"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="auto_sync_enabled"/>
                                <div class="text-muted">
                                    Enable automatic synchronization with biometric devices
                                </div>
                                <div class="content-group" attrs="{'invisible': [('auto_sync_enabled', '=', False)]}">
                                    <div class="mt16">
                                        <label for="sync_interval" class="o_light_label"/>
                                        <field name="sync_interval" class="oe_inline"/> minutes
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="auto_create_daily_records"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="auto_create_daily_records"/>
                                <div class="text-muted">
                                    Automatically create daily attendance records for all employees
                                </div>
                            </div>
                        </div>
                        
                        <div class="o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="auto_mark_absent"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="auto_mark_absent"/>
                                <div class="text-muted">
                                    Automatically mark employees as absent after threshold time
                                </div>
                            </div>
                        </div>
                        
                        <div class="o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="allow_manual_entry"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="allow_manual_entry"/>
                                <div class="text-muted">
                                    Allow manual check-in/check-out entries
                                </div>
                                <div class="content-group" attrs="{'invisible': [('allow_manual_entry', '=', False)]}">
                                    <div class="mt16">
                                        <field name="require_approval_manual"/>
                                        <label for="require_approval_manual" class="o_light_label"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h2>Notifications</h2>
                        <div class="row mt16 o_settings_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="late_notification"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="late_notification"/>
                                    <div class="text-muted">
                                        Send notifications for late arrivals
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="absence_notification"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="absence_notification"/>
                                    <div class="text-muted">
                                        Send notifications for absences
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h2>Default Settings</h2>
                        <div class="row mt16 o_settings_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="default_work_rule_id"/>
                                    <div class="text-muted">
                                        Default work rule for new employees
                                    </div>
                                    <div class="content-group">
                                        <div class="mt16">
                                            <field name="default_work_rule_id" class="oe_inline"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="data_retention_days"/>
                                    <div class="text-muted">
                                        Number of days to keep attendance data (0 = keep forever)
                                    </div>
                                    <div class="content-group">
                                        <div class="mt16">
                                            <field name="data_retention_days" class="oe_inline"/> days
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h2>Actions</h2>
                        <div class="row mt16 o_settings_container">
                            <div class="col-12 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <button name="action_sync_all_devices" string="Sync All Devices" type="object" class="btn-primary"/>
                                    <button name="action_create_daily_records" string="Create Daily Records" type="object" class="btn-secondary"/>
                                    <button name="action_mark_absent" string="Mark Absent Employees" type="object" class="btn-secondary"/>
                                    <button name="action_cleanup_old_data" string="Cleanup Old Data" type="object" class="btn-warning"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Biometric Configuration Action -->
    <record id="action_biometric_config_settings" model="ir.actions.act_window">
        <field name="name">Biometric Attendance Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">biometric.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module': 'biometric_attendance'}</field>
    </record>
</odoo>
