<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Attendance Categories -->
    <record id="module_category_biometric_attendance" model="ir.module.category">
        <field name="name">Biometric Attendance</field>
        <field name="description">Manage biometric attendance system permissions</field>
        <field name="sequence">10</field>
    </record>

    <!-- User Group: Biometric User -->
    <record id="group_biometric_user" model="res.groups">
        <field name="name">Biometric User</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">Basic biometric attendance access - can view own attendance records</field>
    </record>

    <!-- User Group: Biometric Officer -->
    <record id="group_biometric_officer" model="res.groups">
        <field name="name">Biometric Officer</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="comment">Biometric attendance officer - can manage attendance records and employees</field>
    </record>

    <!-- User Group: Biometric Manager -->
    <record id="group_biometric_manager" model="res.groups">
        <field name="name">Biometric Manager</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('group_biometric_officer'))]"/>
        <field name="comment">Biometric attendance manager - full access to all features including devices and configuration</field>
    </record>

    <!-- Record Rules for Biometric Employee -->
    <record id="biometric_employee_rule_user" model="ir.rule">
        <field name="name">Biometric Employee: User can see own record</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[('user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_employee_rule_officer" model="ir.rule">
        <field name="name">Biometric Employee: Officer can see all</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_employee_rule_manager" model="ir.rule">
        <field name="name">Biometric Employee: Manager can do everything</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Record Rules for Biometric Attendance -->
    <record id="biometric_attendance_rule_user" model="ir.rule">
        <field name="name">Biometric Attendance: User can see own records</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_attendance_rule_officer" model="ir.rule">
        <field name="name">Biometric Attendance: Officer can see all</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_attendance_rule_manager" model="ir.rule">
        <field name="name">Biometric Attendance: Manager can do everything</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Record Rules for Biometric Device -->
    <record id="biometric_device_rule_officer" model="ir.rule">
        <field name="name">Biometric Device: Officer can see all</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_device_rule_manager" model="ir.rule">
        <field name="name">Biometric Device: Manager can do everything</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Record Rules for Biometric Work Rule -->
    <record id="biometric_work_rule_rule_officer" model="ir.rule">
        <field name="name">Biometric Work Rule: Officer can see all</field>
        <field name="model_id" ref="model_biometric_work_rule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_work_rule_rule_manager" model="ir.rule">
        <field name="name">Biometric Work Rule: Manager can do everything</field>
        <field name="model_id" ref="model_biometric_work_rule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
