# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_facturx
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2022-01-24 09:06+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "ConformanceLevel"
msgstr "ConformanceLevel"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "DocumentFileName"
msgstr "DocumentFileName"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "DocumentType"
msgstr "DocumentType"

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr "Toon valuta"

#. module: account_edi_facturx
#: model:ir.model,name:account_edi_facturx.model_account_edi_format
msgid "EDI format"
msgstr "EDI formaat"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Factur-X PDFA Extension Schema"
msgstr "Factur-X PDFA Extension Schema"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "INVOICE"
msgstr "FACTUUR"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "Factuur gegenereerd door Odoo"

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it before trying again to import."
msgstr ""
"De valuta (%s) in uw import is niet actief in deze databank.\n"
"Activeer het voordat u opnieuw probeert te importeren."

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid "No information about the journal or the type of invoice is passed"
msgstr ""
"Er wordt geen informatie over het dagboek of het type factuur doorgegeven"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Text"
msgstr "Tekst"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "The actual version of the Factur-X XML schema"
msgstr "De daadwerkelijke versie van het Factur-X XML-schema"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "The conformance level of the embedded Factur-X data"
msgstr "Het conformiteitsniveau van de ingebedde Factur-X-gegevens"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Version"
msgstr "Versie"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "external"
msgstr "extern"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "name of the embedded XML invoice file"
msgstr "naam van het ingesloten XML-factuurbestand"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_facturx_export
msgid "urn:cen.eu:en16931:2017"
msgstr "urn:cen.eu:en16931:2017"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
