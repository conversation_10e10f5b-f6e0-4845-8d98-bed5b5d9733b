id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_biometric_device_user,biometric.device.user,model_biometric_device,group_biometric_user,0,0,0,0
access_biometric_device_officer,biometric.device.officer,model_biometric_device,group_biometric_officer,1,0,0,0
access_biometric_device_manager,biometric.device.manager,model_biometric_device,group_biometric_manager,1,1,1,1
access_biometric_employee_user,biometric.employee.user,model_biometric_employee,group_biometric_user,1,0,0,0
access_biometric_employee_officer,biometric.employee.officer,model_biometric_employee,group_biometric_officer,1,1,1,0
access_biometric_employee_manager,biometric.employee.manager,model_biometric_employee,group_biometric_manager,1,1,1,1
access_biometric_attendance_user,biometric.attendance.user,model_biometric_attendance,group_biometric_user,1,0,0,0
access_biometric_attendance_officer,biometric.attendance.officer,model_biometric_attendance,group_biometric_officer,1,1,1,0
access_biometric_attendance_manager,biometric.attendance.manager,model_biometric_attendance,group_biometric_manager,1,1,1,1
access_biometric_work_rule_user,biometric.work.rule.user,model_biometric_work_rule,group_biometric_user,1,0,0,0
access_biometric_work_rule_officer,biometric.work.rule.officer,model_biometric_work_rule,group_biometric_officer,1,0,0,0
access_biometric_work_rule_manager,biometric.work.rule.manager,model_biometric_work_rule,group_biometric_manager,1,1,1,1
access_biometric_config_settings_manager,biometric.config.settings.manager,model_biometric_config_settings,group_biometric_manager,1,1,1,0
