<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#875A7B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5A4A6B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="128" height="128" rx="20" fill="url(#grad1)"/>
  
  <!-- Fingerprint icon -->
  <g transform="translate(24, 24)">
    <!-- Outer fingerprint lines -->
    <path d="M40 10 C55 10, 70 25, 70 40 C70 55, 55 70, 40 70 C25 70, 10 55, 10 40 C10 25, 25 10, 40 10" 
          fill="none" stroke="white" stroke-width="3" opacity="0.8"/>
    
    <path d="M40 20 C50 20, 60 30, 60 40 C60 50, 50 60, 40 60 C30 60, 20 50, 20 40 C20 30, 30 20, 40 20" 
          fill="none" stroke="white" stroke-width="2.5" opacity="0.9"/>
    
    <path d="M40 30 C45 30, 50 35, 50 40 C50 45, 45 50, 40 50 C35 50, 30 45, 30 40 C30 35, 35 30, 40 30" 
          fill="none" stroke="white" stroke-width="2" opacity="1"/>
    
    <!-- Center dot -->
    <circle cx="40" cy="40" r="3" fill="white"/>
    
    <!-- Clock/time indicator -->
    <g transform="translate(55, 15)">
      <circle cx="0" cy="0" r="8" fill="white" opacity="0.9"/>
      <line x1="0" y1="0" x2="0" y2="-5" stroke="#875A7B" stroke-width="1.5"/>
      <line x1="0" y1="0" x2="3" y2="0" stroke="#875A7B" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Bottom text area -->
  <rect x="0" y="100" width="128" height="28" fill="rgba(255,255,255,0.1)"/>
  <text x="64" y="118" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    BIOMETRIC
  </text>
</svg>
