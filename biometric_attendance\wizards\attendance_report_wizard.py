# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class AttendanceReportWizard(models.TransientModel):
    _name = 'biometric.attendance.report.wizard'
    _description = 'Attendance Report Wizard'

    date_from = fields.Date('Date From', required=True, default=lambda self: fields.Date.today().replace(day=1))
    date_to = fields.Date('Date To', required=True, default=fields.Date.today)
    employee_ids = fields.Many2many('biometric.employee', string='Employees')
    device_ids = fields.Many2many('biometric.device', string='Devices')
    department = fields.Char('Department')
    report_type = fields.Selection([
        ('detailed', 'Detailed Report'),
        ('summary', 'Summary Report'),
        ('late', 'Late Arrival Report'),
        ('absence', 'Absence Report'),
        ('overtime', 'Overtime Report')
    ], string='Report Type', required=True, default='detailed')
    
    include_weekends = fields.Boolean('Include Weekends', default=False)
    include_holidays = fields.Boolean('Include Holidays', default=False)
    group_by_department = fields.Boolean('Group by Department', default=False)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise ValidationError(_('Date From must be before Date To'))
            
            # Check for reasonable date range (not more than 1 year)
            if (wizard.date_to - wizard.date_from).days > 365:
                raise ValidationError(_('Date range cannot exceed 365 days'))
    
    def action_generate_report(self):
        """Generate the selected report"""
        self.ensure_one()
        
        # Build domain for attendance records
        domain = [
            ('attendance_date', '>=', self.date_from),
            ('attendance_date', '<=', self.date_to)
        ]
        
        if self.employee_ids:
            domain.append(('employee_id', 'in', self.employee_ids.ids))
        
        if self.device_ids:
            domain.append(('device_id', 'in', self.device_ids.ids))
        
        if self.department:
            domain.append(('employee_id.department', 'ilike', self.department))
        
        # Filter by report type
        if self.report_type == 'late':
            domain.append(('is_late', '=', True))
        elif self.report_type == 'absence':
            domain.append(('is_absent', '=', True))
        elif self.report_type == 'overtime':
            domain.append(('overtime_hours', '>', 0))
        
        if not self.include_holidays:
            domain.append(('is_holiday', '=', False))
        
        # Get attendance records
        attendance_records = self.env['biometric.attendance'].search(domain)
        
        # Generate report based on type
        if self.report_type == 'detailed':
            return self._generate_detailed_report(attendance_records)
        elif self.report_type == 'summary':
            return self._generate_summary_report(attendance_records)
        elif self.report_type == 'late':
            return self._generate_late_report(attendance_records)
        elif self.report_type == 'absence':
            return self._generate_absence_report(attendance_records)
        elif self.report_type == 'overtime':
            return self._generate_overtime_report(attendance_records)
    
    def _generate_detailed_report(self, records):
        """Generate detailed attendance report"""
        return {
            'type': 'ir.actions.report',
            'report_name': 'biometric_attendance.report_attendance_document',
            'report_type': 'qweb-pdf',
            'data': {
                'date_from': self.date_from,
                'date_to': self.date_to,
                'employee_ids': self.employee_ids,
            },
            'context': {
                'active_ids': records.ids,
            }
        }
    
    def _generate_summary_report(self, records):
        """Generate summary report with statistics"""
        # Calculate summary data
        employee_summary = []
        
        for employee in self.employee_ids or self.env['biometric.employee'].search([]):
            emp_records = records.filtered(lambda r: r.employee_id == employee)
            
            if emp_records:
                summary = {
                    'employee': employee,
                    'present_days': len(emp_records.filtered(lambda r: r.status == 'present')),
                    'late_days': len(emp_records.filtered(lambda r: r.is_late)),
                    'absent_days': len(emp_records.filtered(lambda r: r.is_absent)),
                    'total_hours': sum(emp_records.mapped('worked_hours')),
                    'overtime_hours': sum(emp_records.mapped('overtime_hours')),
                }
                employee_summary.append(summary)
        
        return {
            'type': 'ir.actions.report',
            'report_name': 'biometric_attendance.report_attendance_summary_document',
            'report_type': 'qweb-pdf',
            'data': {
                'date_from': self.date_from,
                'date_to': self.date_to,
                'employee_summary': employee_summary,
            }
        }
    
    def _generate_late_report(self, records):
        """Generate late arrival report"""
        # Calculate late summary
        late_summary = []
        
        for employee in records.mapped('employee_id'):
            emp_late_records = records.filtered(lambda r: r.employee_id == employee and r.is_late)
            
            if emp_late_records:
                total_late_minutes = sum(emp_late_records.mapped('late_minutes'))
                summary = {
                    'employee': employee.name,
                    'late_days': len(emp_late_records),
                    'total_late_minutes': total_late_minutes,
                    'avg_late_minutes': total_late_minutes / len(emp_late_records) if emp_late_records else 0,
                }
                late_summary.append(summary)
        
        return {
            'type': 'ir.actions.report',
            'report_name': 'biometric_attendance.report_late_arrival_document',
            'report_type': 'qweb-pdf',
            'data': {
                'date_from': self.date_from,
                'date_to': self.date_to,
                'late_summary': late_summary,
            },
            'context': {
                'active_ids': records.ids,
            }
        }
    
    def _generate_absence_report(self, records):
        """Generate absence report"""
        # Calculate absence summary
        absence_summary = []
        
        for employee in records.mapped('employee_id'):
            emp_records = records.filtered(lambda r: r.employee_id == employee)
            absent_records = emp_records.filtered(lambda r: r.is_absent)
            
            if absent_records:
                # Calculate working days in period
                working_days = self._calculate_working_days(employee, self.date_from, self.date_to)
                absence_rate = (len(absent_records) / working_days * 100) if working_days > 0 else 0
                
                summary = {
                    'employee': employee.name,
                    'department': employee.department,
                    'absent_days': len(absent_records),
                    'working_days': working_days,
                    'absence_rate': absence_rate,
                }
                absence_summary.append(summary)
        
        return {
            'type': 'ir.actions.report',
            'report_name': 'biometric_attendance.report_absence_document',
            'report_type': 'qweb-pdf',
            'data': {
                'date_from': self.date_from,
                'date_to': self.date_to,
                'absence_summary': absence_summary,
            },
            'context': {
                'active_ids': records.ids,
            }
        }
    
    def _generate_overtime_report(self, records):
        """Generate overtime report"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Overtime Report',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,pivot,graph',
            'domain': [('id', 'in', records.ids)],
            'context': {
                'search_default_group_employee': 1,
                'group_by': ['employee_id'],
            }
        }
    
    def _calculate_working_days(self, employee, date_from, date_to):
        """Calculate working days for employee in given period"""
        if not employee.work_rule_id:
            return 0
        
        working_days = 0
        current_date = date_from
        
        while current_date <= date_to:
            if employee.work_rule_id.is_working_day(current_date):
                working_days += 1
            current_date += timedelta(days=1)
        
        return working_days
    
    def action_export_excel(self):
        """Export report data to Excel"""
        # This would require xlsxwriter or similar library
        # Implementation would depend on specific requirements
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Export'),
                'message': _('Excel export functionality would be implemented here'),
                'type': 'info',
            }
        }
