# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import timedelta


class BiometricConfig(models.TransientModel):
    _name = 'biometric.config.settings'
    _inherit = 'res.config.settings'
    _description = 'Biometric Attendance Configuration'

    # Synchronization Settings
    auto_sync_enabled = fields.<PERSON><PERSON>an(
        'Enable Automatic Synchronization',
        config_parameter='biometric_attendance.auto_sync_enabled',
        default=True
    )
    sync_interval = fields.Integer(
        'Sync Interval (minutes)',
        config_parameter='biometric_attendance.sync_interval',
        default=30,
        help='Interval for automatic synchronization in minutes'
    )
    
    # Attendance Settings
    auto_create_daily_records = fields.Bo<PERSON>an(
        'Auto Create Daily Records',
        config_parameter='biometric_attendance.auto_create_daily_records',
        default=True,
        help='Automatically create attendance records for all employees daily'
    )
    auto_mark_absent = fields.Bo<PERSON>an(
        'Auto Mark Absent',
        config_parameter='biometric_attendance.auto_mark_absent',
        default=True,
        help='Automatically mark employees as absent after threshold time'
    )
    
    # Notification Settings
    late_notification = fields.<PERSON><PERSON><PERSON>(
        'Late Arrival Notifications',
        config_parameter='biometric_attendance.late_notification',
        default=False,
        help='Send notifications for late arrivals'
    )
    absence_notification = fields.Boolean(
        'Absence Notifications',
        config_parameter='biometric_attendance.absence_notification',
        default=False,
        help='Send notifications for absences'
    )
    
    # Default Work Rule
    default_work_rule_id = fields.Many2one(
        'biometric.work.rule',
        string='Default Work Rule',
        config_parameter='biometric_attendance.default_work_rule_id',
        help='Default work rule for new employees'
    )
    
    # Data Retention
    data_retention_days = fields.Integer(
        'Data Retention (days)',
        config_parameter='biometric_attendance.data_retention_days',
        default=365,
        help='Number of days to keep attendance data (0 = keep forever)'
    )
    
    # Security Settings
    allow_manual_entry = fields.Boolean(
        'Allow Manual Entry',
        config_parameter='biometric_attendance.allow_manual_entry',
        default=True,
        help='Allow manual check-in/check-out entries'
    )
    require_approval_manual = fields.Boolean(
        'Require Approval for Manual Entries',
        config_parameter='biometric_attendance.require_approval_manual',
        default=False,
        help='Manual entries require supervisor approval'
    )
    
    @api.model
    def get_values(self):
        res = super(BiometricConfig, self).get_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        res.update(
            auto_sync_enabled=ICPSudo.get_param('biometric_attendance.auto_sync_enabled', True),
            sync_interval=int(ICPSudo.get_param('biometric_attendance.sync_interval', 30)),
            auto_create_daily_records=ICPSudo.get_param('biometric_attendance.auto_create_daily_records', True),
            auto_mark_absent=ICPSudo.get_param('biometric_attendance.auto_mark_absent', True),
            late_notification=ICPSudo.get_param('biometric_attendance.late_notification', False),
            absence_notification=ICPSudo.get_param('biometric_attendance.absence_notification', False),
            default_work_rule_id=int(ICPSudo.get_param('biometric_attendance.default_work_rule_id', 0)) or False,
            data_retention_days=int(ICPSudo.get_param('biometric_attendance.data_retention_days', 365)),
            allow_manual_entry=ICPSudo.get_param('biometric_attendance.allow_manual_entry', True),
            require_approval_manual=ICPSudo.get_param('biometric_attendance.require_approval_manual', False),
        )
        return res
    
    def set_values(self):
        super(BiometricConfig, self).set_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        ICPSudo.set_param('biometric_attendance.auto_sync_enabled', self.auto_sync_enabled)
        ICPSudo.set_param('biometric_attendance.sync_interval', self.sync_interval)
        ICPSudo.set_param('biometric_attendance.auto_create_daily_records', self.auto_create_daily_records)
        ICPSudo.set_param('biometric_attendance.auto_mark_absent', self.auto_mark_absent)
        ICPSudo.set_param('biometric_attendance.late_notification', self.late_notification)
        ICPSudo.set_param('biometric_attendance.absence_notification', self.absence_notification)
        ICPSudo.set_param('biometric_attendance.default_work_rule_id', self.default_work_rule_id.id or 0)
        ICPSudo.set_param('biometric_attendance.data_retention_days', self.data_retention_days)
        ICPSudo.set_param('biometric_attendance.allow_manual_entry', self.allow_manual_entry)
        ICPSudo.set_param('biometric_attendance.require_approval_manual', self.require_approval_manual)
    
    def action_sync_all_devices(self):
        """Sync all active devices"""
        devices = self.env['biometric.device'].search([('active', '=', True)])
        for device in devices:
            device.sync_all_data()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sync Started'),
                'message': _('Synchronization started for all active devices'),
                'type': 'info',
            }
        }
    
    def action_create_daily_records(self):
        """Create daily attendance records for all employees"""
        self.env['biometric.attendance'].create_daily_attendance_records()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Records Created'),
                'message': _('Daily attendance records created for all employees'),
                'type': 'success',
            }
        }
    
    def action_mark_absent(self):
        """Mark absent employees"""
        self.env['biometric.attendance'].mark_absent_employees()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Absent Marked'),
                'message': _('Absent employees have been marked'),
                'type': 'success',
            }
        }
    
    def action_cleanup_old_data(self):
        """Clean up old attendance data based on retention policy"""
        if self.data_retention_days > 0:
            cutoff_date = fields.Date.today() - timedelta(days=self.data_retention_days)
            old_records = self.env['biometric.attendance'].search([
                ('attendance_date', '<', cutoff_date)
            ])
            count = len(old_records)
            old_records.unlink()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Data Cleaned'),
                    'message': _('%d old attendance records have been deleted') % count,
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Action'),
                    'message': _('Data retention is disabled (set to 0 days)'),
                    'type': 'info',
                }
            }
