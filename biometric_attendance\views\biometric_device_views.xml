<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Device Tree View -->
    <record id="view_biometric_device_tree" model="ir.ui.view">
        <field name="name">biometric.device.tree</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <tree string="Biometric Devices" decoration-success="status=='connected'" decoration-danger="status=='error'">
                <field name="name"/>
                <field name="device_type"/>
                <field name="connection_type"/>
                <field name="ip_address"/>
                <field name="port"/>
                <field name="status"/>
                <field name="total_employees"/>
                <field name="total_records"/>
                <field name="last_sync"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Biometric Device Form View -->
    <record id="view_biometric_device_form" model="ir.ui.view">
        <field name="name">biometric.device.form</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <form string="Biometric Device">
                <header>
                    <button name="test_connection" string="Test Connection" type="object" class="btn-primary"/>
                    <button name="sync_employees" string="Sync Employees" type="object" class="btn-secondary"/>
                    <button name="sync_attendance" string="Sync Attendance" type="object" class="btn-secondary"/>
                    <button name="sync_all_data" string="Sync All Data" type="object" class="btn-success"/>
                    <field name="status" widget="statusbar" statusbar_visible="disconnected,connected,error"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_employees" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="total_employees" widget="statinfo" string="Employees"/>
                        </button>
                        <button name="action_view_attendance" type="object" class="oe_stat_button" icon="fa-clock-o">
                            <field name="total_records" widget="statinfo" string="Records"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Inactive" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Device Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="device_info">
                            <field name="device_type"/>
                            <field name="connection_type"/>
                            <field name="device_id"/>
                            <field name="active"/>
                        </group>
                        <group name="connection_info">
                            <field name="ip_address" attrs="{'required': [('connection_type', '=', 'tcp')], 'invisible': [('connection_type', '!=', 'tcp')]}"/>
                            <field name="port" attrs="{'required': [('connection_type', '=', 'tcp')], 'invisible': [('connection_type', '!=', 'tcp')]}"/>
                            <field name="serial_port" attrs="{'required': [('connection_type', '=', 'serial')], 'invisible': [('connection_type', '!=', 'serial')]}"/>
                            <field name="baud_rate" attrs="{'invisible': [('connection_type', '!=', 'serial')]}"/>
                            <field name="password"/>
                            <field name="timeout"/>
                        </group>
                    </group>
                    
                    <group name="status_info">
                        <group>
                            <field name="last_sync"/>
                        </group>
                        <group>
                            <!-- Empty group for layout -->
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Employees" name="employees">
                            <field name="employee_ids" readonly="1">
                                <tree>
                                    <field name="employee_id"/>
                                    <field name="name"/>
                                    <field name="department"/>
                                    <field name="status"/>
                                    <field name="last_attendance"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Attendance Records" name="attendance">
                            <field name="attendance_ids" readonly="1">
                                <tree>
                                    <field name="employee_id"/>
                                    <field name="attendance_date"/>
                                    <field name="check_in"/>
                                    <field name="check_out"/>
                                    <field name="status"/>
                                    <field name="worked_hours"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Biometric Device Search View -->
    <record id="view_biometric_device_search" model="ir.ui.view">
        <field name="name">biometric.device.search</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <search string="Search Devices">
                <field name="name"/>
                <field name="device_type"/>
                <field name="ip_address"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Connected" name="connected" domain="[('status', '=', 'connected')]"/>
                <filter string="Disconnected" name="disconnected" domain="[('status', '=', 'disconnected')]"/>
                <filter string="Error" name="error" domain="[('status', '=', 'error')]"/>
                <group expand="0" string="Group By">
                    <filter string="Device Type" name="group_device_type" context="{'group_by': 'device_type'}"/>
                    <filter string="Connection Type" name="group_connection_type" context="{'group_by': 'connection_type'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Biometric Device Action -->
    <record id="action_biometric_device" model="ir.actions.act_window">
        <field name="name">Biometric Devices</field>
        <field name="res_model">biometric.device</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_biometric_device_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first biometric device!
            </p>
            <p>
                Configure biometric devices to connect and synchronize employee attendance data.
                Supports various device types including ZKTeco, Hikvision, and others.
            </p>
        </field>
    </record>
</odoo>
