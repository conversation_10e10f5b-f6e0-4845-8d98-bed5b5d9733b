<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Attendance Report Template -->
    <template id="report_attendance_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Attendance Report</h2>
                        <p>
                            <strong>Period:</strong> 
                            <span t-esc="date_from"/> to <span t-esc="date_to"/>
                        </p>
                        <t t-if="employee_ids">
                            <p>
                                <strong>Employees:</strong> 
                                <span t-esc="', '.join([emp.name for emp in employee_ids])"/>
                            </p>
                        </t>
                    </div>
                </div>

                <table class="table table-sm o_main_table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Employee</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Worked Hours</th>
                            <th>Late Minutes</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="docs" t-as="attendance">
                            <tr>
                                <td><span t-field="attendance.attendance_date"/></td>
                                <td><span t-field="attendance.employee_id.name"/></td>
                                <td><span t-field="attendance.check_in"/></td>
                                <td><span t-field="attendance.check_out"/></td>
                                <td><span t-field="attendance.worked_hours"/> hrs</td>
                                <td><span t-field="attendance.late_minutes"/> min</td>
                                <td>
                                    <span t-if="attendance.status == 'present'" class="badge badge-success">Present</span>
                                    <span t-elif="attendance.status == 'late'" class="badge badge-warning">Late</span>
                                    <span t-elif="attendance.status == 'absent'" class="badge badge-danger">Absent</span>
                                    <span t-elif="attendance.status == 'early_departure'" class="badge badge-info">Early Departure</span>
                                    <span t-else="" class="badge badge-secondary"><span t-field="attendance.status"/></span>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Attendance Report Action -->
    <record id="action_report_attendance" model="ir.actions.report">
        <field name="name">Attendance Report</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_attendance_document</field>
        <field name="report_file">biometric_attendance.report_attendance_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Summary Report Template -->
    <template id="report_attendance_summary_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Attendance Summary Report</h2>
                        <p>
                            <strong>Period:</strong> 
                            <span t-esc="date_from"/> to <span t-esc="date_to"/>
                        </p>
                    </div>
                </div>

                <t t-foreach="employee_summary" t-as="employee_data">
                    <div class="row mt-4">
                        <div class="col-12">
                            <h4><span t-esc="employee_data['employee'].name"/> (ID: <span t-esc="employee_data['employee'].employee_id"/>)</h4>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-success"><span t-esc="employee_data['present_days']"/></h5>
                                    <p class="card-text">Present Days</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-warning"><span t-esc="employee_data['late_days']"/></h5>
                                    <p class="card-text">Late Days</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-danger"><span t-esc="employee_data['absent_days']"/></h5>
                                    <p class="card-text">Absent Days</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-info"><span t-esc="round(employee_data['total_hours'], 2)"/></h5>
                                    <p class="card-text">Total Hours</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Summary Report Action -->
    <record id="action_report_attendance_summary" model="ir.actions.report">
        <field name="name">Attendance Summary Report</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_attendance_summary_document</field>
        <field name="report_file">biometric_attendance.report_attendance_summary_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
