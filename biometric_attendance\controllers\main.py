# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


class BiometricAttendanceController(http.Controller):
    
    @http.route('/biometric/api/checkin', type='json', auth='user', methods=['POST'])
    def api_checkin(self, employee_id, device_id, **kwargs):
        """API endpoint for check-in from external systems"""
        try:
            employee = request.env['biometric.employee'].search([
                ('employee_id', '=', employee_id),
                ('device_id', '=', device_id)
            ], limit=1)
            
            if not employee:
                return {'error': 'Employee not found'}
            
            result = employee.action_manual_checkin()
            return {'success': True, 'message': 'Check-in successful'}
            
        except Exception as e:
            _logger.error("API check-in error: %s" % str(e))
            return {'error': str(e)}
    
    @http.route('/biometric/api/checkout', type='json', auth='user', methods=['POST'])
    def api_checkout(self, employee_id, device_id, **kwargs):
        """API endpoint for check-out from external systems"""
        try:
            employee = request.env['biometric.employee'].search([
                ('employee_id', '=', employee_id),
                ('device_id', '=', device_id)
            ], limit=1)
            
            if not employee:
                return {'error': 'Employee not found'}
            
            result = employee.action_manual_checkout()
            return {'success': True, 'message': 'Check-out successful'}
            
        except Exception as e:
            _logger.error("API check-out error: %s" % str(e))
            return {'error': str(e)}
    
    @http.route('/biometric/api/sync', type='json', auth='user', methods=['POST'])
    def api_sync_device(self, device_id, **kwargs):
        """API endpoint for device synchronization"""
        try:
            device = request.env['biometric.device'].browse(device_id)
            if not device.exists():
                return {'error': 'Device not found'}
            
            result = device.sync_all_data()
            return {'success': True, 'message': 'Sync completed'}
            
        except Exception as e:
            _logger.error("API sync error: %s" % str(e))
            return {'error': str(e)}
    
    @http.route('/biometric/dashboard', type='http', auth='user', website=True)
    def dashboard(self, **kwargs):
        """Biometric attendance dashboard"""
        try:
            # Get dashboard data
            today = request.env['biometric.attendance'].search([
                ('attendance_date', '=', request.env.context.get('today', ''))
            ])
            
            devices = request.env['biometric.device'].search([('active', '=', True)])
            employees = request.env['biometric.employee'].search([('active', '=', True)])
            
            values = {
                'today_attendance': today,
                'devices': devices,
                'employees': employees,
                'page_name': 'biometric_dashboard',
            }
            
            return request.render('biometric_attendance.dashboard_template', values)
            
        except Exception as e:
            _logger.error("Dashboard error: %s" % str(e))
            return request.render('web.http_error', {'error': str(e)})
    
    @http.route('/biometric/api/employee_status', type='json', auth='user', methods=['GET'])
    def get_employee_status(self, employee_id=None, **kwargs):
        """Get current status of employee(s)"""
        try:
            domain = [('active', '=', True)]
            if employee_id:
                domain.append(('employee_id', '=', employee_id))
            
            employees = request.env['biometric.employee'].search(domain)
            
            result = []
            for emp in employees:
                result.append({
                    'employee_id': emp.employee_id,
                    'name': emp.name,
                    'status': emp.status,
                    'last_attendance': emp.last_attendance.isoformat() if emp.last_attendance else None,
                    'department': emp.department,
                })
            
            return {'success': True, 'employees': result}
            
        except Exception as e:
            _logger.error("Employee status API error: %s" % str(e))
            return {'error': str(e)}
    
    @http.route('/biometric/api/attendance_report', type='json', auth='user', methods=['GET'])
    def get_attendance_report(self, date_from=None, date_to=None, employee_ids=None, **kwargs):
        """Get attendance report data"""
        try:
            domain = []
            
            if date_from:
                domain.append(('attendance_date', '>=', date_from))
            if date_to:
                domain.append(('attendance_date', '<=', date_to))
            if employee_ids:
                domain.append(('employee_id', 'in', employee_ids))
            
            attendance_records = request.env['biometric.attendance'].search(domain)
            
            result = []
            for record in attendance_records:
                result.append({
                    'date': record.attendance_date.isoformat(),
                    'employee_id': record.employee_id.employee_id,
                    'employee_name': record.employee_id.name,
                    'check_in': record.check_in.isoformat() if record.check_in else None,
                    'check_out': record.check_out.isoformat() if record.check_out else None,
                    'worked_hours': record.worked_hours,
                    'late_minutes': record.late_minutes,
                    'status': record.status,
                    'is_manual': record.manual_entry,
                })
            
            return {'success': True, 'records': result}
            
        except Exception as e:
            _logger.error("Attendance report API error: %s" % str(e))
            return {'error': str(e)}
