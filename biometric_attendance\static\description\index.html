<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Biometric Attendance System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #875A7B; color: white; padding: 20px; text-align: center; }
        .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #875A7B; }
        .screenshot { text-align: center; margin: 20px 0; }
        .screenshot img { max-width: 100%; height: auto; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Biometric Attendance System for Odoo 15</h1>
        <p>Complete biometric attendance management with device integration</p>
    </div>

    <div class="feature">
        <h2>🔗 Device Integration</h2>
        <p>Connect to various biometric devices including ZKTeco, Hikvision, Suprema, and others via TCP/IP or Serial Port.</p>
    </div>

    <div class="feature">
        <h2>👥 Employee Management</h2>
        <p>Independent employee management system with biometric data synchronization from devices.</p>
    </div>

    <div class="feature">
        <h2>⏰ Attendance Tracking</h2>
        <p>Automatic calculation of late arrivals, early departures, overtime, and absences based on configurable work rules.</p>
    </div>

    <div class="feature">
        <h2>📊 Comprehensive Reports</h2>
        <p>Detailed reports for attendance, late arrivals, absences, and employee performance analysis.</p>
    </div>

    <div class="feature">
        <h2>⚙️ Flexible Configuration</h2>
        <p>Configurable work rules, tolerance settings, overtime policies, and automated synchronization.</p>
    </div>

    <div class="feature">
        <h2>🔒 Security & Permissions</h2>
        <p>Role-based access control with different permission levels for users, officers, and managers.</p>
    </div>

    <div class="feature">
        <h2>🔄 Automated Operations</h2>
        <p>Scheduled synchronization, automatic absence marking, and data cleanup with configurable intervals.</p>
    </div>

    <div class="feature">
        <h2>📱 API Integration</h2>
        <p>RESTful API endpoints for external system integration and mobile applications.</p>
    </div>

    <h2>Installation</h2>
    <ol>
        <li>Copy the module to your Odoo addons directory</li>
        <li>Update the app list in Odoo</li>
        <li>Install the "Biometric Attendance System" module</li>
        <li>Configure your biometric devices</li>
        <li>Set up work rules and employee assignments</li>
        <li>Start synchronizing attendance data</li>
    </ol>

    <h2>Requirements</h2>
    <ul>
        <li>Odoo 15.0+</li>
        <li>Python packages: pyserial (for serial connections)</li>
        <li>Network access to biometric devices (for TCP/IP connections)</li>
    </ul>

    <h2>Support</h2>
    <p>This module is designed to be completely independent and doesn't require any other HR modules. 
    It provides a complete attendance management solution for organizations using biometric devices.</p>

    <div style="text-align: center; margin-top: 40px; color: #666;">
        <p>Developed for Odoo 15 | Version 1.0.0</p>
    </div>
</body>
</html>
