# BSSIC Requests Demo Data

## Overview
This directory contains demo data for the BSSIC Requests Management module. The demo data is designed to provide a comprehensive testing environment with realistic request scenarios.

## Demo Data Contents

### Employees
- **20 Demo Employees** with Arabic names and email addresses
- Employees represent different departments and roles
- All employees have proper email format: `<EMAIL>`

### Requests
- **496 Total Requests** across all request types
- **62 Requests per type** for balanced testing
- **Realistic data** with proper field values and relationships

## Request Types Distribution

| Request Type | Count | Description |
|--------------|-------|-------------|
| Password Reset | 62 | Password and account reactivation requests |
| USB Access | 62 | USB storage usage requests |
| Extension | 62 | Project/deadline extension requests |
| Email | 62 | Email account management requests |
| Authorization Delegation | 62 | Authority delegation requests |
| Free Form | 62 | Free form/custom requests |
| Permission | 62 | System permission requests |
| Technical | 62 | Technical support requests |

## State Distribution

| State | Count | Percentage | Description |
|-------|-------|------------|-------------|
| Draft | 77 | 15.5% | Newly created requests |
| Submitted | 95 | 19.2% | Submitted for approval |
| Direct Manager | 106 | 21.4% | Pending direct manager approval |
| Audit Manager | 78 | 15.7% | Pending audit manager approval |
| IT Manager | 44 | 8.9% | Pending IT manager approval |
| Assigned | 23 | 4.6% | Assigned to IT staff |
| In Progress | 25 | 5.0% | Currently being processed |
| Completed | 41 | 8.3% | Successfully completed |
| Rejected | 7 | 1.4% | Rejected requests |

## Request Type Specific Fields

### Password Reset Requests
- **Device Types**: Internet, System, SWIFT, Other
- **Request Reasons**: Password Reset, Account Reactivation
- **Usernames**: Based on employee names

### USB Access Requests
- **Purposes**: Data transfer, Backup files, Software installation, Training materials
- **Durations**: 1 hour to 1 week
- **Data Types**: Documents, Software, Financial reports, Training materials

### Extension Requests
- **Durations**: 3 days to 1 month
- **Reasons**: Additional requirements, Vendor delays, Testing issues, Resource constraints

### Email Requests
- **Email Types**: New, Password Reset, 2FA Reset
- **Reasons**: New employee, Forgot password, Lost 2FA device, Department change
- **Agreement**: Properly set for new email requests

### Authorization Delegation Requests
- **Amounts**: Range from 100,000 to 1,000,000
- **Reasons**: Temporary increase, Manager absence, Special project, Month-end processing
- **Date Ranges**: Realistic future date ranges

### Free Form Requests
- **Subjects**: Special access, Emergency correction, Audit investigation, System maintenance
- **Entry Types**: Free entry, Reverse entry
- **Date Ranges**: Realistic operational periods

### Permission Requests
- **Permission Types**: Add, Modify, Delete, Withdraw, Activate, Deactivate
- **Departments**: Accounting, Risk, Back Office Deposits, Forex Exchange
- **Levels**: Level 1, 2, 3 access
- **Validity Periods**: 30-90 days

### Technical Requests
- **Priorities**: 1 (High), 2 (Medium), 3 (Low)
- **Nature**: All marked as technical requests
- **Descriptions**: Server maintenance, Network issues, Software installation, Hardware replacement

## Date Ranges
- **Request Dates**: Distributed over the last 60 days
- **Validity Dates**: Future dates for applicable request types
- **Realistic Timelines**: Proper workflow progression timing

## Installation
The demo data is automatically loaded when installing the module with demo data enabled:

```bash
# Install with demo data
odoo-bin -d your_database -i bssic_requests --load-language=ar_001
```

Or through the Odoo interface:
1. Go to Apps
2. Search for "BSSIC Requests Management"
3. Install with "Install Demo Data" checked

## Usage for Testing

### Workflow Testing
- Test approval workflows with requests in different states
- Verify state transitions and permissions
- Test rejection and completion processes

### User Role Testing
- Test different user group permissions
- Verify field visibility based on request types
- Test assignment and delegation features

### Reporting Testing
- Generate reports with substantial data
- Test filtering and grouping features
- Verify performance with realistic data volumes

### Performance Testing
- Test system performance with 500+ records
- Verify search and filter performance
- Test bulk operations

## Data Generation
The demo data was generated using the `generate_demo_data.py` script, which:
- Creates realistic employee records
- Generates requests with proper field relationships
- Distributes states according to realistic workflow patterns
- Ensures data consistency and referential integrity

## Customization
To modify the demo data:
1. Edit the `generate_demo_data.py` script
2. Run the script to generate new data
3. Replace the existing demo data file
4. Reinstall the module

## Notes
- All demo data uses `noupdate="1"` to prevent conflicts during upgrades
- Employee emails are fictional but follow proper format
- Request dates are relative to installation date
- All monetary amounts are in the system's base currency
- Demo data is suitable for training and testing purposes only
