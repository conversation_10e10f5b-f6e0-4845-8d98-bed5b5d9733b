odoo.define('biometric_attendance.dashboard', function (require) {
'use strict';

var AbstractAction = require('web.AbstractAction');
var core = require('web.core');
var rpc = require('web.rpc');
var QWeb = core.qweb;

var BiometricDashboard = AbstractAction.extend({
    template: 'BiometricDashboard',
    
    init: function(parent, context) {
        this._super(parent, context);
        this.dashboards_templates = ['BiometricDashboard'];
    },

    willStart: function() {
        var self = this;
        return this._super().then(function() {
            return self.fetch_data();
        });
    },

    start: function() {
        var self = this;
        return this._super().then(function() {
            self.render_dashboards();
            self.render_graphs();
        });
    },

    fetch_data: function() {
        var self = this;
        return rpc.query({
            model: 'biometric.attendance',
            method: 'get_dashboard_data',
        }).then(function(result) {
            self.data = result;
        });
    },

    render_dashboards: function() {
        var self = this;
        if (this.data) {
            this.$('.o_biometric_dashboard').html(QWeb.render('BiometricDashboardMain', {
                widget: this,
                data: this.data
            }));
        }
    },

    render_graphs: function() {
        this.render_attendance_chart();
        this.render_status_chart();
    },

    render_attendance_chart: function() {
        var ctx = this.$('#attendanceChart')[0].getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.attendance_chart.labels,
                datasets: [{
                    label: 'Daily Attendance',
                    data: this.data.attendance_chart.data,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    },

    render_status_chart: function() {
        var ctx = this.$('#statusChart')[0].getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Present', 'Late', 'Absent'],
                datasets: [{
                    data: [
                        this.data.status_summary.present,
                        this.data.status_summary.late,
                        this.data.status_summary.absent
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true
            }
        });
    },

    on_dashboard_action_clicked: function(ev) {
        ev.preventDefault();
        var $action = $(ev.currentTarget);
        var action_name = $action.attr('name');
        
        if (action_name) {
            this.do_action(action_name);
        }
    },

});

core.action_registry.add('biometric_dashboard', BiometricDashboard);

return BiometricDashboard;

});
