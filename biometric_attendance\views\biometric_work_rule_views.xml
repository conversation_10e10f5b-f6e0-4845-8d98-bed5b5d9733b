<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Biometric Work Rule Tree View -->
    <record id="view_biometric_work_rule_tree" model="ir.ui.view">
        <field name="name">biometric.work.rule.tree</field>
        <field name="model">biometric.work.rule</field>
        <field name="arch" type="xml">
            <tree string="Work Rules">
                <field name="name"/>
                <field name="work_start_time" widget="float_time"/>
                <field name="work_end_time" widget="float_time"/>
                <field name="total_work_hours"/>
                <field name="working_days_count"/>
                <field name="late_tolerance"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Biometric Work Rule Form View -->
    <record id="view_biometric_work_rule_form" model="ir.ui.view">
        <field name="name">biometric.work.rule.form</field>
        <field name="model">biometric.work.rule</field>
        <field name="arch" type="xml">
            <form string="Work Rule">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_employees" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="employee_ids" widget="statinfo" string="Employees"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Inactive" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Work Rule Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <field name="active"/>
                    </group>
                    
                    <group name="work_schedule" string="Work Schedule">
                        <group>
                            <field name="work_start_time" widget="float_time"/>
                            <field name="work_end_time" widget="float_time"/>
                            <field name="break_duration"/>
                            <field name="total_work_hours"/>
                        </group>
                        <group>
                            <field name="minimum_work_hours"/>
                            <field name="working_days_count"/>
                        </group>
                    </group>
                    
                    <group name="working_days" string="Working Days">
                        <group>
                            <field name="monday"/>
                            <field name="tuesday"/>
                            <field name="wednesday"/>
                            <field name="thursday"/>
                        </group>
                        <group>
                            <field name="friday"/>
                            <field name="saturday"/>
                            <field name="sunday"/>
                        </group>
                    </group>
                    
                    <group name="tolerance" string="Tolerance Settings">
                        <group>
                            <field name="late_tolerance"/>
                            <field name="early_departure_tolerance"/>
                        </group>
                        <group>
                            <field name="absence_after"/>
                        </group>
                    </group>
                    
                    <group name="overtime" string="Overtime Settings">
                        <group>
                            <field name="overtime_after"/>
                            <field name="overtime_rate"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Employees" name="employees">
                            <field name="employee_ids" readonly="1">
                                <tree>
                                    <field name="employee_id"/>
                                    <field name="name"/>
                                    <field name="device_id"/>
                                    <field name="department"/>
                                    <field name="status"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Biometric Work Rule Search View -->
    <record id="view_biometric_work_rule_search" model="ir.ui.view">
        <field name="name">biometric.work.rule.search</field>
        <field name="model">biometric.work.rule</field>
        <field name="arch" type="xml">
            <search string="Search Work Rules">
                <field name="name"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Total Work Hours" name="group_work_hours" context="{'group_by': 'total_work_hours'}"/>
                    <filter string="Working Days" name="group_working_days" context="{'group_by': 'working_days_count'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Biometric Work Rule Action -->
    <record id="action_biometric_work_rule" model="ir.actions.act_window">
        <field name="name">Work Rules</field>
        <field name="res_model">biometric.work.rule</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_biometric_work_rule_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first work rule!
            </p>
            <p>
                Work rules define working hours, tolerance settings, and overtime policies
                for calculating attendance status and working hours.
            </p>
        </field>
    </record>
</odoo>
