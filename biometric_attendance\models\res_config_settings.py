# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Biometric Attendance Settings
    biometric_auto_sync = fields.Boolean(
        'Enable Auto Sync',
        config_parameter='biometric_attendance.auto_sync_enabled'
    )
    biometric_sync_interval = fields.Integer(
        'Sync Interval (minutes)',
        config_parameter='biometric_attendance.sync_interval',
        default=30
    )
    biometric_default_work_rule = fields.Many2one(
        'biometric.work.rule',
        'Default Work Rule',
        config_parameter='biometric_attendance.default_work_rule_id'
    )
    biometric_allow_manual = fields.Bo<PERSON>an(
        'Allow Manual Entries',
        config_parameter='biometric_attendance.allow_manual_entry'
    )
    biometric_data_retention = fields.Integer(
        'Data Retention (days)',
        config_parameter='biometric_attendance.data_retention_days',
        default=365
    )

    @api.model
    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        res.update(
            biometric_auto_sync=ICPSudo.get_param('biometric_attendance.auto_sync_enabled', False),
            biometric_sync_interval=int(ICPSudo.get_param('biometric_attendance.sync_interval', 30)),
            biometric_default_work_rule=int(ICPSudo.get_param('biometric_attendance.default_work_rule_id', 0)) or False,
            biometric_allow_manual=ICPSudo.get_param('biometric_attendance.allow_manual_entry', True),
            biometric_data_retention=int(ICPSudo.get_param('biometric_attendance.data_retention_days', 365)),
        )
        return res

    def set_values(self):
        super(ResConfigSettings, self).set_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        ICPSudo.set_param('biometric_attendance.auto_sync_enabled', self.biometric_auto_sync)
        ICPSudo.set_param('biometric_attendance.sync_interval', self.biometric_sync_interval)
        ICPSudo.set_param('biometric_attendance.default_work_rule_id', self.biometric_default_work_rule.id or 0)
        ICPSudo.set_param('biometric_attendance.allow_manual_entry', self.biometric_allow_manual)
        ICPSudo.set_param('biometric_attendance.data_retention_days', self.biometric_data_retention)
