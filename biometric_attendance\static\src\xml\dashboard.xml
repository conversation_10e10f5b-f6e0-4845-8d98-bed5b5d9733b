<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="BiometricDashboard">
        <div class="o_biometric_dashboard">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">Biometric Attendance Dashboard</h2>
                    </div>
                </div>
                
                <!-- Loading placeholder -->
                <div class="row" t-if="!widget.data">
                    <div class="col-12 text-center">
                        <i class="fa fa-spinner fa-spin fa-3x"></i>
                        <p>Loading dashboard data...</p>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <t t-name="BiometricDashboardMain">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><t t-esc="data.total_employees"/></h4>
                                <p class="card-text">Total Employees</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fa fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><t t-esc="data.present_today"/></h4>
                                <p class="card-text">Present Today</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fa fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><t t-esc="data.late_today"/></h4>
                                <p class="card-text">Late Today</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fa fa-clock-o fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><t t-esc="data.absent_today"/></h4>
                                <p class="card-text">Absent Today</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fa fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Weekly Attendance Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="attendanceChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Today's Status</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Device Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <t t-foreach="data.devices" t-as="device">
                                <div class="col-md-4 mb-3">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h6 class="card-title"><t t-esc="device.name"/></h6>
                                                    <p class="card-text small">
                                                        <span t-if="device.status == 'connected'" class="badge badge-success">Connected</span>
                                                        <span t-elif="device.status == 'error'" class="badge badge-danger">Error</span>
                                                        <span t-else="" class="badge badge-secondary">Disconnected</span>
                                                    </p>
                                                    <p class="card-text small">
                                                        Employees: <t t-esc="device.total_employees"/>
                                                    </p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fa fa-desktop fa-lg"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="#" class="btn btn-primary btn-block" name="biometric_attendance.action_biometric_attendance_today">
                                    <i class="fa fa-calendar"></i> Today's Attendance
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="#" class="btn btn-info btn-block" name="biometric_attendance.action_biometric_employee">
                                    <i class="fa fa-users"></i> Manage Employees
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="#" class="btn btn-success btn-block" name="biometric_attendance.action_biometric_device">
                                    <i class="fa fa-desktop"></i> Manage Devices
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="#" class="btn btn-warning btn-block" name="biometric_attendance.action_attendance_report_wizard">
                                    <i class="fa fa-file-text"></i> Generate Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Employee</th>
                                        <th>Action</th>
                                        <th>Device</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="data.recent_activity" t-as="activity">
                                        <tr>
                                            <td><t t-esc="activity.time"/></td>
                                            <td><t t-esc="activity.employee"/></td>
                                            <td><t t-esc="activity.action"/></td>
                                            <td><t t-esc="activity.device"/></td>
                                            <td>
                                                <span t-if="activity.status == 'present'" class="badge badge-success">Present</span>
                                                <span t-elif="activity.status == 'late'" class="badge badge-warning">Late</span>
                                                <span t-else="" class="badge badge-info"><t t-esc="activity.status"/></span>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
