<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Default Work Rules -->
    <record id="work_rule_standard" model="biometric.work.rule">
        <field name="name">Standard Work Rule (8 Hours)</field>
        <field name="work_start_time">8.0</field>
        <field name="work_end_time">17.0</field>
        <field name="break_duration">1.0</field>
        <field name="monday">True</field>
        <field name="tuesday">True</field>
        <field name="wednesday">True</field>
        <field name="thursday">True</field>
        <field name="friday">True</field>
        <field name="saturday">False</field>
        <field name="sunday">False</field>
        <field name="late_tolerance">15.0</field>
        <field name="early_departure_tolerance">15.0</field>
        <field name="overtime_after">8.0</field>
        <field name="overtime_rate">1.5</field>
        <field name="absence_after">2.0</field>
        <field name="minimum_work_hours">8.0</field>
    </record>

    <record id="work_rule_flexible" model="biometric.work.rule">
        <field name="name">Flexible Work Rule (7 Hours)</field>
        <field name="work_start_time">9.0</field>
        <field name="work_end_time">17.0</field>
        <field name="break_duration">1.0</field>
        <field name="monday">True</field>
        <field name="tuesday">True</field>
        <field name="wednesday">True</field>
        <field name="thursday">True</field>
        <field name="friday">True</field>
        <field name="saturday">False</field>
        <field name="sunday">False</field>
        <field name="late_tolerance">30.0</field>
        <field name="early_departure_tolerance">30.0</field>
        <field name="overtime_after">7.0</field>
        <field name="overtime_rate">1.5</field>
        <field name="absence_after">3.0</field>
        <field name="minimum_work_hours">7.0</field>
    </record>

    <record id="work_rule_shift" model="biometric.work.rule">
        <field name="name">Evening Shift Rule</field>
        <field name="work_start_time">14.0</field>
        <field name="work_end_time">22.0</field>
        <field name="break_duration">1.0</field>
        <field name="monday">True</field>
        <field name="tuesday">True</field>
        <field name="wednesday">True</field>
        <field name="thursday">True</field>
        <field name="friday">True</field>
        <field name="saturday">False</field>
        <field name="sunday">False</field>
        <field name="late_tolerance">10.0</field>
        <field name="early_departure_tolerance">10.0</field>
        <field name="overtime_after">8.0</field>
        <field name="overtime_rate">1.5</field>
        <field name="absence_after">1.0</field>
        <field name="minimum_work_hours">8.0</field>
    </record>

    <!-- Night Shift Work Rule (for future use) -->
    <!-- Note: Night shifts require special handling in the calculation logic -->
    <!-- This is commented out to avoid validation errors during installation -->
    <!--
    <record id="work_rule_night" model="biometric.work.rule">
        <field name="name">Night Shift Rule (22:00-06:00)</field>
        <field name="work_start_time">22.0</field>
        <field name="work_end_time">6.0</field>
        <field name="break_duration">1.0</field>
        <field name="monday">True</field>
        <field name="tuesday">True</field>
        <field name="wednesday">True</field>
        <field name="thursday">True</field>
        <field name="friday">True</field>
        <field name="saturday">True</field>
        <field name="sunday">True</field>
        <field name="late_tolerance">10.0</field>
        <field name="early_departure_tolerance">10.0</field>
        <field name="overtime_after">8.0</field>
        <field name="overtime_rate">2.0</field>
        <field name="absence_after">1.0</field>
        <field name="minimum_work_hours">8.0</field>
    </record>
    -->

    <!-- Default Configuration Parameters -->
    <record id="config_auto_sync_enabled" model="ir.config_parameter">
        <field name="key">biometric_attendance.auto_sync_enabled</field>
        <field name="value">True</field>
    </record>

    <record id="config_sync_interval" model="ir.config_parameter">
        <field name="key">biometric_attendance.sync_interval</field>
        <field name="value">30</field>
    </record>

    <record id="config_auto_create_daily_records" model="ir.config_parameter">
        <field name="key">biometric_attendance.auto_create_daily_records</field>
        <field name="value">True</field>
    </record>

    <record id="config_auto_mark_absent" model="ir.config_parameter">
        <field name="key">biometric_attendance.auto_mark_absent</field>
        <field name="value">True</field>
    </record>

    <record id="config_allow_manual_entry" model="ir.config_parameter">
        <field name="key">biometric_attendance.allow_manual_entry</field>
        <field name="value">True</field>
    </record>

    <record id="config_data_retention_days" model="ir.config_parameter">
        <field name="key">biometric_attendance.data_retention_days</field>
        <field name="value">365</field>
    </record>

    <record id="config_default_work_rule" model="ir.config_parameter">
        <field name="key">biometric_attendance.default_work_rule_id</field>
        <field name="value" ref="work_rule_standard"/>
    </record>
</odoo>
