# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timedelta


class BiometricWorkRule(models.Model):
    _name = 'biometric.work.rule'
    _description = 'Biometric Work Rule'
    _order = 'name'

    name = fields.Char('Rule Name', required=True)
    active = fields.<PERSON><PERSON><PERSON>('Active', default=True)
    
    # Work Schedule
    work_start_time = fields.Float('Work Start Time', required=True, default=8.0,
                                   help='Work start time in 24-hour format (e.g., 8.5 for 8:30 AM)')
    work_end_time = fields.Float('Work End Time', required=True, default=17.0,
                                 help='Work end time in 24-hour format (e.g., 17.5 for 5:30 PM)')
    break_duration = fields.Float('Break Duration (hours)', default=1.0)
    
    # Working Days
    monday = fields.Bo<PERSON>an('Monday', default=True)
    tuesday = fields.Boolean('Tuesday', default=True)
    wednesday = fields.<PERSON><PERSON><PERSON>('Wednesday', default=True)
    thursday = fields.<PERSON><PERSON><PERSON>('Thursday', default=True)
    friday = fields.<PERSON><PERSON>an('Friday', default=True)
    saturday = fields.<PERSON><PERSON>an('Saturday', default=False)
    sunday = fields.<PERSON><PERSON>an('Sunday', default=False)
    
    # Tolerance Settings
    late_tolerance = fields.Float('Late Tolerance (minutes)', default=15.0,
                                  help='Grace period for late arrival')
    early_departure_tolerance = fields.Float('Early Departure Tolerance (minutes)', default=15.0,
                                             help='Grace period for early departure')
    
    # Overtime Settings
    overtime_after = fields.Float('Overtime After (hours)', default=8.0,
                                  help='Hours after which overtime is calculated')
    overtime_rate = fields.Float('Overtime Rate', default=1.5,
                                 help='Overtime rate multiplier')
    
    # Absence Settings
    absence_after = fields.Float('Mark Absent After (hours)', default=2.0,
                                 help='Hours after work start time to mark as absent')
    
    # Minimum Work Hours
    minimum_work_hours = fields.Float('Minimum Work Hours', default=8.0,
                                      help='Minimum required work hours per day')
    
    # Relations
    employee_ids = fields.One2many('biometric.employee', 'work_rule_id', string='Employees')
    
    # Computed Fields
    total_work_hours = fields.Float('Total Work Hours', compute='_compute_total_work_hours', store=True)
    working_days_count = fields.Integer('Working Days Count', compute='_compute_working_days', store=True)
    
    @api.depends('work_start_time', 'work_end_time', 'break_duration')
    def _compute_total_work_hours(self):
        for rule in self:
            if rule.work_end_time > rule.work_start_time:
                # Normal day shift
                rule.total_work_hours = rule.work_end_time - rule.work_start_time - rule.break_duration
            elif rule.work_start_time > rule.work_end_time:
                # Night shift spanning midnight (e.g., 22:00 to 6:00)
                rule.total_work_hours = (24 - rule.work_start_time) + rule.work_end_time - rule.break_duration
            else:
                rule.total_work_hours = 0
    
    @api.depends('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
    def _compute_working_days(self):
        for rule in self:
            days = [rule.monday, rule.tuesday, rule.wednesday, rule.thursday, 
                   rule.friday, rule.saturday, rule.sunday]
            rule.working_days_count = sum(days)
    
    @api.constrains('work_start_time', 'work_end_time')
    def _check_work_times(self):
        for rule in self:
            # Allow night shifts where end time is next day (e.g., 22:00 to 6:00)
            # We'll handle this logic in the calculation methods
            if rule.work_start_time < 0 or rule.work_start_time >= 24:
                raise ValidationError(_('Work start time must be between 0 and 24.'))
            if rule.work_end_time < 0 or rule.work_end_time > 24:
                raise ValidationError(_('Work end time must be between 0 and 24.'))
            # For same-day shifts, end time should be after start time
            if rule.work_start_time < rule.work_end_time:
                # Normal day shift - no additional validation needed
                pass
            elif rule.work_start_time > rule.work_end_time:
                # Night shift spanning midnight - this is allowed
                pass
            else:
                # Start and end times are the same
                raise ValidationError(_('Work start and end times cannot be the same.'))
    
    @api.constrains('late_tolerance', 'early_departure_tolerance')
    def _check_tolerance(self):
        for rule in self:
            if rule.late_tolerance < 0 or rule.late_tolerance > 120:
                raise ValidationError(_('Late tolerance must be between 0 and 120 minutes.'))
            if rule.early_departure_tolerance < 0 or rule.early_departure_tolerance > 120:
                raise ValidationError(_('Early departure tolerance must be between 0 and 120 minutes.'))
    
    @api.constrains('working_days_count')
    def _check_working_days(self):
        for rule in self:
            if rule.working_days_count == 0:
                raise ValidationError(_('At least one working day must be selected.'))
    
    def get_working_days(self):
        """Return list of working days (0=Monday, 6=Sunday)"""
        self.ensure_one()
        working_days = []
        days = [self.monday, self.tuesday, self.wednesday, self.thursday, 
               self.friday, self.saturday, self.sunday]
        for i, is_working in enumerate(days):
            if is_working:
                working_days.append(i)
        return working_days
    
    def is_working_day(self, date):
        """Check if given date is a working day according to this rule"""
        self.ensure_one()
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        working_days = self.get_working_days()
        return weekday in working_days
    
    def calculate_late_minutes(self, check_in_time):
        """Calculate late minutes based on work rule"""
        self.ensure_one()
        if not check_in_time:
            return 0

        # Convert work start time to datetime
        work_start_hour = int(self.work_start_time)
        work_start_minute = int((self.work_start_time - work_start_hour) * 60)

        work_start_datetime = check_in_time.replace(
            hour=work_start_hour,
            minute=work_start_minute,
            second=0,
            microsecond=0
        )

        # Handle night shifts - if work starts after check-in date, it might be previous day
        if self.work_start_time > self.work_end_time:  # Night shift
            # If check-in is early morning, work start was previous day
            if check_in_time.hour < 12 and self.work_start_time > 12:
                work_start_datetime = work_start_datetime - timedelta(days=1)

        if check_in_time > work_start_datetime:
            late_minutes = (check_in_time - work_start_datetime).total_seconds() / 60
            return max(0, late_minutes - self.late_tolerance)
        return 0
    
    def calculate_early_departure_minutes(self, check_out_time):
        """Calculate early departure minutes based on work rule"""
        self.ensure_one()
        if not check_out_time:
            return 0

        # Convert work end time to datetime
        work_end_hour = int(self.work_end_time)
        work_end_minute = int((self.work_end_time - work_end_hour) * 60)

        work_end_datetime = check_out_time.replace(
            hour=work_end_hour,
            minute=work_end_minute,
            second=0,
            microsecond=0
        )

        # Handle night shifts - if work ends before check-out date, it might be next day
        if self.work_start_time > self.work_end_time:  # Night shift
            # If check-out is late evening, work end is next day
            if check_out_time.hour > 12 and self.work_end_time < 12:
                work_end_datetime = work_end_datetime + timedelta(days=1)

        if check_out_time < work_end_datetime:
            early_minutes = (work_end_datetime - check_out_time).total_seconds() / 60
            return max(0, early_minutes - self.early_departure_tolerance)
        return 0
    
    def calculate_overtime_hours(self, worked_hours):
        """Calculate overtime hours based on work rule"""
        self.ensure_one()
        if worked_hours > self.overtime_after:
            return worked_hours - self.overtime_after
        return 0

    def action_view_employees(self):
        """View employees using this work rule"""
        self.ensure_one()
        return {
            'name': _('Employees - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.employee',
            'view_mode': 'tree,form',
            'domain': [('work_rule_id', '=', self.id)],
            'context': {'default_work_rule_id': self.id},
        }
