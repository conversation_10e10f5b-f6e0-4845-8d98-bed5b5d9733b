<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit General Settings to add Biometric section -->
    <record id="res_config_settings_view_form_inherit_biometric" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.biometric</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Biometric Attendance" string="Biometric Attendance" data-key="biometric_attendance">
                    <h2>Biometric Attendance System</h2>
                    
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="biometric_auto_sync"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="biometric_auto_sync"/>
                                <div class="text-muted">
                                    Enable automatic synchronization with biometric devices
                                </div>
                                <div class="content-group" attrs="{'invisible': [('biometric_auto_sync', '=', False)]}">
                                    <div class="mt16">
                                        <label for="biometric_sync_interval" class="o_light_label"/>
                                        <field name="biometric_sync_interval" class="oe_inline"/> minutes
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="biometric_allow_manual"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="biometric_allow_manual"/>
                                <div class="text-muted">
                                    Allow manual check-in/check-out entries
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_right_pane">
                                <label for="biometric_default_work_rule"/>
                                <div class="text-muted">
                                    Default work rule for new employees
                                </div>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="biometric_default_work_rule" class="oe_inline"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_right_pane">
                                <label for="biometric_data_retention"/>
                                <div class="text-muted">
                                    Number of days to keep attendance data (0 = keep forever)
                                </div>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="biometric_data_retention" class="oe_inline"/> days
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
