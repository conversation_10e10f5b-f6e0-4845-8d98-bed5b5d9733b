<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Absence Report Template -->
    <template id="report_absence_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Absence Report</h2>
                        <p>
                            <strong>Period:</strong> 
                            <span t-esc="date_from"/> to <span t-esc="date_to"/>
                        </p>
                    </div>
                </div>

                <table class="table table-sm o_main_table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Employee</th>
                            <th>Department</th>
                            <th>Expected Work Day</th>
                            <th>Status</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="docs" t-as="attendance">
                            <tr t-if="attendance.is_absent">
                                <td><span t-field="attendance.attendance_date"/></td>
                                <td><span t-field="attendance.employee_id.name"/></td>
                                <td><span t-field="attendance.employee_id.department"/></td>
                                <td>
                                    <t t-if="attendance.employee_id.work_rule_id">
                                        <span class="badge badge-info">Yes</span>
                                    </t>
                                    <t t-else="">
                                        <span class="badge badge-secondary">No Rule</span>
                                    </t>
                                </td>
                                <td>
                                    <span class="badge badge-danger">Absent</span>
                                </td>
                                <td><span t-field="attendance.notes"/></td>
                            </tr>
                        </t>
                    </tbody>
                </table>

                <!-- Summary Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Absence Summary</h4>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Total Absent Days</th>
                                    <th>Working Days in Period</th>
                                    <th>Absence Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="absence_summary" t-as="summary">
                                    <tr>
                                        <td><span t-esc="summary['employee']"/></td>
                                        <td><span t-esc="summary['department']"/></td>
                                        <td><span t-esc="summary['absent_days']"/></td>
                                        <td><span t-esc="summary['working_days']"/></td>
                                        <td>
                                            <span t-esc="round(summary['absence_rate'], 1)"/>%
                                            <t t-if="summary['absence_rate'] > 10">
                                                <span class="badge badge-danger ml-2">High</span>
                                            </t>
                                            <t t-elif="summary['absence_rate'] > 5">
                                                <span class="badge badge-warning ml-2">Medium</span>
                                            </t>
                                            <t t-else="">
                                                <span class="badge badge-success ml-2">Low</span>
                                            </t>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Absence Report Action -->
    <record id="action_report_absence" model="ir.actions.report">
        <field name="name">Absence Report</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_absence_document</field>
        <field name="report_file">biometric_attendance.report_absence_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Department Absence Report Template -->
    <template id="report_department_absence_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Department Absence Analysis</h2>
                        <p>
                            <strong>Period:</strong> 
                            <span t-esc="date_from"/> to <span t-esc="date_to"/>
                        </p>
                    </div>
                </div>

                <!-- Department Summary -->
                <div class="row">
                    <div class="col-12">
                        <h4>Department Overview</h4>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Total Employees</th>
                                    <th>Total Absent Days</th>
                                    <th>Average Absence Rate</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="department_summary" t-as="dept_data">
                                    <tr>
                                        <td><span t-esc="dept_data['department']"/></td>
                                        <td><span t-esc="dept_data['total_employees']"/></td>
                                        <td><span t-esc="dept_data['total_absent_days']"/></td>
                                        <td><span t-esc="round(dept_data['avg_absence_rate'], 1)"/>%</td>
                                        <td>
                                            <t t-if="dept_data['avg_absence_rate'] > 15">
                                                <span class="badge badge-danger">Critical</span>
                                            </t>
                                            <t t-elif="dept_data['avg_absence_rate'] > 10">
                                                <span class="badge badge-warning">High</span>
                                            </t>
                                            <t t-elif="dept_data['avg_absence_rate'] > 5">
                                                <span class="badge badge-info">Medium</span>
                                            </t>
                                            <t t-else="">
                                                <span class="badge badge-success">Good</span>
                                            </t>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Recommendations</h4>
                        <div class="alert alert-info">
                            <ul>
                                <li>Departments with absence rates above 10% require immediate attention</li>
                                <li>Consider implementing attendance improvement programs for high-absence departments</li>
                                <li>Review work rules and policies for departments with critical absence rates</li>
                                <li>Investigate patterns in absence data to identify root causes</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Department Absence Report Action -->
    <record id="action_report_department_absence" model="ir.actions.report">
        <field name="name">Department Absence Analysis</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_department_absence_document</field>
        <field name="report_file">biometric_attendance.report_department_absence_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
