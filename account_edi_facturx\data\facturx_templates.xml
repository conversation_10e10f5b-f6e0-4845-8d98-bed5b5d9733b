<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="account_invoice_line_facturx_export">
            <t t-set="line" t-value="line_vals['line']"/>
            <t  xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <ram:IncludedSupplyChainTradeLineItem>
                    <!-- Line number. -->
                    <ram:AssociatedDocumentLineDocument>
                        <ram:LineID t-esc="line_vals['index']"/>
                    </ram:AssociatedDocumentLineDocument>

                    <!-- Product. -->
                    <ram:SpecifiedTradeProduct>
                        <ram:GlobalID
                            schemeID="0160"
                            t-if="line.product_id and line.product_id.barcode"
                            t-esc="line.product_id.barcode"/>
                        <ram:SellerAssignedID
                            t-if="line.product_id and line.product_id.default_code"
                            t-esc="line.product_id.default_code"/>
                        <!-- TODO:
                        <ram:Name t-esc="line.product_id.name or line.name"/>
                        <ram:Description t-esc="line.name" t-if="line.name != line.product_id.name"/>
                        -->
                        <ram:Name t-esc="line.name"/>
                    </ram:SpecifiedTradeProduct>

                    <!-- Amounts. -->
                    <ram:SpecifiedLineTradeAgreement>
                        <!-- Line information, with discount and unit price separate -->
                        <ram:GrossPriceProductTradePrice>
                            <ram:ChargeAmount
                                t-esc="format_monetary(line_vals['gross_price_total_unit'], record.currency_id)"/>

                            <!-- Discount. -->
                            <ram:AppliedTradeAllowanceCharge t-if="line.discount">
                                <ram:ChargeIndicator>
                                    <udt:Indicator>false</udt:Indicator>
                                </ram:ChargeIndicator>
                                <ram:ActualAmount t-esc="format_monetary(line_vals['price_discount_unit'], record.currency_id)"/>
                            </ram:AppliedTradeAllowanceCharge>
                        </ram:GrossPriceProductTradePrice>
                        <!-- Line unit price, with discount applied -->
                        <ram:NetPriceProductTradePrice>
                            <ram:ChargeAmount
                                t-esc="format_monetary(line_vals['price_subtotal_unit'], record.currency_id)"/>
                        </ram:NetPriceProductTradePrice>
                    </ram:SpecifiedLineTradeAgreement>

                    <!-- Quantity. -->
                    <ram:SpecifiedLineTradeDelivery>
                        <ram:BilledQuantity t-att-unitCode="line_vals['unece_uom_code']" t-esc="line.quantity"/>
                    </ram:SpecifiedLineTradeDelivery>

                    <ram:SpecifiedLineTradeSettlement>
                        <t t-foreach="tax_details['invoice_line_tax_details'][line]['tax_details'].values()"
                           t-as="tax_detail_vals">
                            <ram:ApplicableTradeTax t-if="tax_detail_vals['amount_type'] == 'percent'">
                                <ram:TypeCode>VAT</ram:TypeCode>
                                <ram:CategoryCode t-esc="tax_detail_vals['unece_tax_category_code']"/>
                                <ram:RateApplicablePercent t-esc="tax_detail_vals['amount']"/>
                            </ram:ApplicableTradeTax>
                        </t>
                        <!-- Subtotal. -->
                        <ram:SpecifiedTradeSettlementLineMonetarySummation>
                            <ram:LineTotalAmount
                                t-esc="format_monetary(line.price_subtotal, record.currency_id)"/>
                        </ram:SpecifiedTradeSettlementLineMonetarySummation>
                    </ram:SpecifiedLineTradeSettlement>

                </ram:IncludedSupplyChainTradeLineItem>
            </t>
        </template>

        <template id="account_invoice_partner_facturx_export">
            <t  xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <!-- Contact. -->
                <ram:Name t-if="partner.name" t-esc="partner.name"/>

                <ram:SpecifiedLegalOrganization t-if="SpecifiedLegalOrganization">
                    <ram:ID t-esc="SpecifiedLegalOrganization"/>
                </ram:SpecifiedLegalOrganization>

                <ram:DefinedTradeContact>
                    <ram:PersonName t-esc="partner.name"/>
                    <ram:TelephoneUniversalCommunication t-if="partner.phone or partner.mobile">
                        <ram:CompleteNumber t-esc="partner.phone or partner.mobile"/>
                    </ram:TelephoneUniversalCommunication>
                    <ram:EmailURIUniversalCommunication t-if="partner.email">
                        <ram:URIID schemeID='SMTP' t-esc="partner.email"/>
                    </ram:EmailURIUniversalCommunication>
                </ram:DefinedTradeContact>

                <!-- Address. -->
                <ram:PostalTradeAddress>
                    <ram:PostcodeCode t-if="partner.zip" t-esc="partner.zip"/>
                    <ram:LineOne t-if="partner.street" t-esc="partner.street"/>
                    <ram:LineTwo t-if="partner.street2" t-esc="partner.street2"/>
                    <ram:CityName t-if="partner.city" t-esc="partner.city"/>
                    <ram:CountryID t-if="partner.country_id" t-esc="partner.country_id.code"/>
                </ram:PostalTradeAddress>
            </t>
        </template>

        <template id="account_invoice_facturx_export">
            <rsm:CrossIndustryInvoice
                xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <!-- Factur-x level:
                    * minimum or basicwl:   urn:factur-x.eu:1p0...
                    * basic:                urn:cen.eu:en16931:2017:compliant:factur-x.eu:1p0:basic
                    * en16931:              urn:cen.eu:en16931:2017
                -->
                <rsm:ExchangedDocumentContext>
                    <ram:GuidelineSpecifiedDocumentContextParameter>
                        <ram:ID>urn:cen.eu:en16931:2017#conformant#urn:factur-x.eu:1p0:extended</ram:ID>
                    </ram:GuidelineSpecifiedDocumentContextParameter>
                </rsm:ExchangedDocumentContext>

                <!-- Document Headers. -->
                <rsm:ExchangedDocument>
                    <ram:ID t-esc="record.name"/>
                    <ram:TypeCode t-esc="'381' if 'refund' in record.move_type else '380'"/>
                    <ram:IssueDateTime>
                        <udt:DateTimeString format="102" t-esc="format_date(record.invoice_date)"/>
                    </ram:IssueDateTime>
                    <ram:IncludedNote t-if="not is_html_empty(record.narration)">
                        <ram:Content t-esc="record.narration"/>
                    </ram:IncludedNote>
                </rsm:ExchangedDocument>

                <rsm:SupplyChainTradeTransaction>
                    <!-- Invoice lines. -->
                    <t t-foreach="invoice_line_vals_list" t-as="line_vals">
                        <t t-call="account_edi_facturx.account_invoice_line_facturx_export"/>
                    </t>

                    <!-- Partners. -->
                    <ram:ApplicableHeaderTradeAgreement>
                        <ram:BuyerReference t-esc="buyer_reference or record.ref"/>

                        <!-- Seller. -->
                        <ram:SellerTradeParty>
                            <!-- Address. -->
                            <t t-call="account_edi_facturx.account_invoice_partner_facturx_export">
                                <t t-set="partner" t-value="record.company_id.partner_id"/>
                                <t t-set="SpecifiedLegalOrganization" t-value="seller_specified_legal_organization"/>
                            </t>

                            <!-- VAT. -->
                            <ram:SpecifiedTaxRegistration t-if="record.company_id.vat">
                                <ram:ID schemeID="VA" t-esc="record.company_id.vat"/>
                            </ram:SpecifiedTaxRegistration>
                        </ram:SellerTradeParty>

                        <!-- Customer. -->
                        <ram:BuyerTradeParty>
                            <!-- Address. -->
                            <t t-call="account_edi_facturx.account_invoice_partner_facturx_export">
                                <t t-set="partner" t-value="record.commercial_partner_id"/>
                                <t t-set="SpecifiedLegalOrganization" t-value="buyer_specified_legal_organization"/>
                            </t>

                            <!-- VAT. -->
                            <ram:SpecifiedTaxRegistration t-if="record.commercial_partner_id.vat">
                                <ram:ID schemeID="VA" t-esc="record.commercial_partner_id.vat"/>
                            </ram:SpecifiedTaxRegistration>
                        </ram:BuyerTradeParty>

                        <!-- Reference. -->
                        <ram:BuyerOrderReferencedDocument>
                            <ram:IssuerAssignedID t-esc="purchase_order_reference or record.payment_reference or record.name"/>
                        </ram:BuyerOrderReferencedDocument>

                        <ram:ContractReferencedDocument t-if="contract_reference">
                            <ram:IssuerAssignedID t-esc="contract_reference"/>
                        </ram:ContractReferencedDocument>
                    </ram:ApplicableHeaderTradeAgreement>

                    <!-- Delivery. Don't make a dependency with sale only for one field. -->
                    <ram:ApplicableHeaderTradeDelivery>
                        <ram:ShipToTradeParty>
                            <t t-call="account_edi_facturx.account_invoice_partner_facturx_export">
                                <t t-if="'partner_shipping_id' in record._fields and record.partner_shipping_id" t-set="partner" t-value="record.partner_shipping_id"/>
                                <t t-else="" t-set="partner" t-value="record.commercial_partner_id"/>
                            </t>
                        </ram:ShipToTradeParty>
                    </ram:ApplicableHeaderTradeDelivery>

                    <!-- Taxes. -->
                    <ram:ApplicableHeaderTradeSettlement>
                        <ram:InvoiceCurrencyCode t-esc="record.currency_id.name"/>

                        <!-- Bank account. -->
                        <ram:SpecifiedTradeSettlementPaymentMeans t-if="record.partner_bank_id.acc_type == 'iban'">
                        <ram:TypeCode>42</ram:TypeCode>
                            <ram:PayeePartyCreditorFinancialAccount>
                                <ram:IBANID t-esc="record.partner_bank_id.sanitized_acc_number"/>
                            </ram:PayeePartyCreditorFinancialAccount>
                        </ram:SpecifiedTradeSettlementPaymentMeans>

                        <!-- Tax Summary. -->
                        <t t-foreach="tax_details['tax_details'].values()" t-as="tax_detail_vals">
                            <ram:ApplicableTradeTax>
                                <ram:CalculatedAmount
                                    t-esc="format_monetary(balance_multiplicator * tax_detail_vals['tax_amount_currency'], record.currency_id)"/>
                                <ram:TypeCode>VAT</ram:TypeCode>
                                <ram:ExemptionReason t-if="tax_detail_vals['unece_tax_category_code'] == 'E'" t-esc="tax_detail_vals['exemption_reason']"/>
                                <ram:ExemptionReason t-if="tax_detail_vals['unece_tax_category_code'] == 'G'" t-esc="'Export outside the EU'"/>
                                <ram:ExemptionReason t-if="tax_detail_vals['unece_tax_category_code'] == 'K'" t-esc="'Intra-community supply'"/>
                                <ram:BasisAmount
                                    t-esc="format_monetary(balance_multiplicator * tax_detail_vals['base_amount_currency'], record.currency_id)"/>
                                <ram:CategoryCode t-esc="tax_detail_vals['unece_tax_category_code']"/>
                                <ram:RateApplicablePercent
                                    t-if="tax_detail_vals['amount_type'] == 'percent'"
                                    t-esc="tax_detail_vals['amount']"/>
                            </ram:ApplicableTradeTax>
                        </t>

                        <ram:BillingSpecifiedPeriod>
                            <ram:StartDateTime>
                                <udt:DateTimeString format="102" t-esc="format_date(record.invoice_date)"/>
                            </ram:StartDateTime>
                        </ram:BillingSpecifiedPeriod>

                        <!-- Payment Term. -->
                        <ram:SpecifiedTradePaymentTerms>
                            <ram:Description t-if="record.invoice_payment_term_id" t-esc="record.invoice_payment_term_id.name"/>
                            <ram:DueDateDateTime t-if="record.invoice_date_due">
                                <udt:DateTimeString format="102" t-esc="format_date(record.invoice_date_due)"/>
                            </ram:DueDateDateTime>
                        </ram:SpecifiedTradePaymentTerms>

                        <!-- Summary. -->
                        <ram:SpecifiedTradeSettlementHeaderMonetarySummation>
                            <ram:LineTotalAmount
                                t-esc="format_monetary(record.amount_untaxed, record.currency_id)"/>
                            <ram:TaxBasisTotalAmount
                                t-esc="format_monetary(record.amount_untaxed, record.currency_id)"/>
                            <ram:TaxTotalAmount
                                t-att-currencyID="record.currency_id.name"
                                t-esc="format_monetary(record.amount_tax, record.currency_id)"/>
                            <ram:GrandTotalAmount
                                t-esc="format_monetary(record.amount_total, record.currency_id)"/>
                            <ram:TotalPrepaidAmount
                                t-esc="format_monetary(record.amount_total - record.amount_residual, record.currency_id)"/>
                            <ram:DuePayableAmount
                                t-esc="format_monetary(record.amount_residual, record.currency_id)"/>
                        </ram:SpecifiedTradeSettlementHeaderMonetarySummation>
                    </ram:ApplicableHeaderTradeSettlement>
                </rsm:SupplyChainTradeTransaction>
            </rsm:CrossIndustryInvoice>
        </template>

        <template id="account_invoice_pdfa_3_facturx_metadata">
            <x:xmpmeta xmlns:x="adobe:ns:meta/">
                <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
                    <rdf:Description xmlns:pdfaid="http://www.aiim.org/pdfa/ns/id/" rdf:about="">
                        <pdfaid:part>3</pdfaid:part>
                        <pdfaid:conformance>B</pdfaid:conformance>
                    </rdf:Description>
                    <rdf:Description xmlns:dc="http://purl.org/dc/elements/1.1/" rdf:about="">
                        <dc:title>
                            <rdf:Alt>
                                <rdf:li t-att="{'xml:lang': 'x-default'}" t-esc="title"/>
                            </rdf:Alt>
                        </dc:title>
                        <dc:creator>
                            <rdf:Seq>
                                <rdf:li>Odoo</rdf:li>
                            </rdf:Seq>
                        </dc:creator>
                        <dc:description>
                            <rdf:Alt>
                                <rdf:li t-att="{'xml:lang': 'x-default'}">Invoice generated by Odoo</rdf:li>
                            </rdf:Alt>
                        </dc:description>
                    </rdf:Description>
                    <rdf:Description xmlns:pdf="http://ns.adobe.com/pdf/1.3/" rdf:about="">
                        <pdf:Producer>Odoo</pdf:Producer>
                    </rdf:Description>
                    <rdf:Description xmlns:xmp="http://ns.adobe.com/xap/1.0/" rdf:about="">
                        <xmp:CreatorTool>Odoo</xmp:CreatorTool>
                        <xmp:CreateDate t-esc="date"/>
                        <xmp:ModifyDate t-esc="date"/>
                    </rdf:Description>
                    <rdf:Description xmlns:pdfaExtension="http://www.aiim.org/pdfa/ns/extension/"
                                     xmlns:pdfaSchema="http://www.aiim.org/pdfa/ns/schema#"
                                     xmlns:pdfaProperty="http://www.aiim.org/pdfa/ns/property#" rdf:about="">
                        <pdfaExtension:schemas>
                            <rdf:Bag>
                                <rdf:li rdf:parseType="Resource">
                                    <pdfaSchema:schema>Factur-X PDFA Extension Schema</pdfaSchema:schema>
                                    <pdfaSchema:namespaceURI>urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#</pdfaSchema:namespaceURI>
                                    <pdfaSchema:prefix>fx</pdfaSchema:prefix>
                                    <pdfaSchema:property>
                                        <rdf:Seq>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name>DocumentFileName</pdfaProperty:name>
                                                <pdfaProperty:valueType>Text</pdfaProperty:valueType>
                                                <pdfaProperty:category>external</pdfaProperty:category>
                                                <pdfaProperty:description>name of the embedded XML invoice file</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name>DocumentType</pdfaProperty:name>
                                                <pdfaProperty:valueType>Text</pdfaProperty:valueType>
                                                <pdfaProperty:category>external</pdfaProperty:category>
                                                <pdfaProperty:description>INVOICE</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name>Version</pdfaProperty:name>
                                                <pdfaProperty:valueType>Text</pdfaProperty:valueType>
                                                <pdfaProperty:category>external</pdfaProperty:category>
                                                <pdfaProperty:description>The actual version of the Factur-X XML schema</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name>ConformanceLevel</pdfaProperty:name>
                                                <pdfaProperty:valueType>Text</pdfaProperty:valueType>
                                                <pdfaProperty:category>external</pdfaProperty:category>
                                                <pdfaProperty:description>The conformance level of the embedded Factur-X data</pdfaProperty:description>
                                            </rdf:li>
                                        </rdf:Seq>
                                    </pdfaSchema:property>
                                </rdf:li>
                            </rdf:Bag>
                        </pdfaExtension:schemas>
                    </rdf:Description>
                    <rdf:Description xmlns:fx="urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#" rdf:about="">
                        <fx:ConformanceLevel>EN 16931</fx:ConformanceLevel>
                        <fx:DocumentFileName>factur-x.xml</fx:DocumentFileName>
                        <fx:DocumentType>INVOICE</fx:DocumentType>
                        <fx:Version>1.0</fx:Version>
                    </rdf:Description>
                </rdf:RDF>
            </x:xmpmeta>
        </template>
    </data>
</odoo>
