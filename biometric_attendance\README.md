# Biometric Attendance System for Odoo 15

A comprehensive biometric attendance management system that integrates with fingerprint devices and provides complete attendance tracking, reporting, and analysis capabilities.

## Features

### 🔗 Device Integration
- Support for multiple biometric device types (ZKTeco, Hikvision, Suprema, Anviz)
- TCP/IP and Serial Port connectivity options
- Real-time device status monitoring
- Automatic and manual data synchronization

### 👥 Employee Management
- Independent employee database synchronized from devices
- Biometric template tracking (fingerprints, face recognition)
- Department and position management
- Work rule assignments

### ⏰ Attendance Tracking
- Automatic check-in/check-out recording
- Late arrival and early departure calculation
- Overtime hours computation
- Absence detection and marking
- Manual entry support with approval workflow

### 📊 Comprehensive Reporting
- Detailed attendance reports
- Late arrival analysis
- Absence tracking and statistics
- Employee performance metrics
- Department-wise analysis
- Exportable reports (PDF)

### ⚙️ Flexible Configuration
- Customizable work rules and schedules
- Tolerance settings for late arrivals
- Overtime policies and rates
- Holiday and weekend handling
- Automated synchronization intervals

### 🔒 Security & Permissions
- Role-based access control
- Three permission levels: User, Officer, Manager
- Record-level security rules
- Audit trail for manual entries

### 🔄 Automated Operations
- Scheduled device synchronization
- Automatic daily record creation
- Absence marking after threshold time
- Data retention and cleanup
- Notification system for late arrivals and absences

### 📱 API Integration
- RESTful API endpoints
- JSON-based communication
- External system integration support
- Mobile application compatibility

## Installation

1. **Download and Extract**
   ```bash
   cd /path/to/odoo/addons
   # Extract the biometric_attendance module here
   ```

2. **Install Dependencies**
   ```bash
   pip install pyserial
   ```

3. **Update Odoo**
   - Restart Odoo server
   - Go to Apps menu
   - Update Apps List
   - Search for "Biometric Attendance System"
   - Click Install

## Configuration

### 1. Device Setup
1. Navigate to **Biometric Attendance > Devices > Biometric Devices**
2. Create a new device record
3. Configure connection settings (IP/Port or Serial Port)
4. Test the connection
5. Activate the device

### 2. Work Rules
1. Go to **Biometric Attendance > Configuration > Work Rules**
2. Create work rules defining:
   - Working hours and days
   - Break duration
   - Late tolerance
   - Overtime settings
3. Assign rules to employees

### 3. Employee Synchronization
1. Use **Sync Employees** button on device form
2. Or use the sync wizard: **Biometric Attendance > Devices > Sync Wizard**
3. Assign work rules to synchronized employees

### 4. Attendance Synchronization
1. Manual sync via device form or sync wizard
2. Enable automatic sync in settings
3. Configure sync intervals

## Usage

### Daily Operations
- **Today's Attendance**: View current day attendance status
- **Manual Check-in/out**: Handle exceptions and corrections
- **Real-time Monitoring**: Track employee status throughout the day

### Reporting
- **Attendance Reports**: Detailed records with filters
- **Late Arrival Reports**: Analysis of tardiness patterns
- **Absence Reports**: Track and analyze absences
- **Performance Analytics**: Employee and department metrics

### Administration
- **Device Management**: Monitor and maintain device connections
- **Work Rule Management**: Update policies and schedules
- **User Permissions**: Manage access levels
- **System Settings**: Configure automation and notifications

## API Endpoints

### Check-in/Check-out
```http
POST /biometric/api/checkin
POST /biometric/api/checkout
Content-Type: application/json

{
    "employee_id": "001",
    "device_id": 1
}
```

### Employee Status
```http
GET /biometric/api/employee_status?employee_id=001
```

### Attendance Report
```http
GET /biometric/api/attendance_report?date_from=2024-01-01&date_to=2024-01-31
```

## Technical Details

### Models
- `biometric.device`: Device configuration and management
- `biometric.employee`: Employee records from devices
- `biometric.attendance`: Attendance records and calculations
- `biometric.work.rule`: Work schedules and policies
- `biometric.config.settings`: System configuration

### Security Groups
- **Biometric User**: View own attendance records
- **Biometric Officer**: Manage attendance and employees
- **Biometric Manager**: Full system access

### Scheduled Actions
- Device synchronization (configurable interval)
- Daily record creation (daily at 00:30)
- Absence marking (hourly)
- Data cleanup (weekly)
- Statistics update (every 6 hours)

## Troubleshooting

### Connection Issues
1. Verify device IP address and port
2. Check network connectivity
3. Ensure device is powered on and accessible
4. Test connection using device form

### Synchronization Problems
1. Check device status and last sync time
2. Verify work rules are assigned to employees
3. Review error logs in Odoo
4. Use manual sync to test specific devices

### Data Inconsistencies
1. Check work rule configurations
2. Verify employee assignments
3. Review manual entry permissions
4. Use attendance report wizard for analysis

## Support and Maintenance

### Regular Maintenance
- Monitor device connections daily
- Review attendance reports weekly
- Update work rules as needed
- Clean up old data based on retention policy

### Performance Optimization
- Configure appropriate sync intervals
- Use filters in reports for large datasets
- Archive old attendance records
- Monitor system resources during sync operations

## Version Information
- **Version**: 1.0.0
- **Odoo Compatibility**: 15.0+
- **License**: LGPL-3
- **Dependencies**: base, web
- **External Dependencies**: pyserial

## Changelog

### Version 1.0.0
- Initial release
- Complete biometric device integration
- Attendance tracking and calculation
- Comprehensive reporting system
- API endpoints for external integration
- Automated synchronization and operations
- Role-based security system

---

For technical support or feature requests, please refer to the module documentation or contact the development team.
