# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class BiometricAttendance(models.Model):
    _name = 'biometric.attendance'
    _description = 'Biometric Attendance Record'
    _order = 'attendance_date desc, check_in desc'

    # Basic Information
    employee_id = fields.Many2one('biometric.employee', string='Employee', required=True, ondelete='cascade')
    device_id = fields.Many2one('biometric.device', string='Device', required=True, ondelete='cascade')
    attendance_date = fields.Date('Attendance Date', required=True, default=fields.Date.today)
    
    # Time Records
    check_in = fields.Datetime('Check In')
    check_out = fields.Datetime('Check Out')
    
    # Calculated Fields
    worked_hours = fields.Float('Worked Hours', compute='_compute_worked_hours', store=True)
    late_minutes = fields.Float('Late Minutes', compute='_compute_attendance_status', store=True)
    early_departure_minutes = fields.Float('Early Departure Minutes', compute='_compute_attendance_status', store=True)
    overtime_hours = fields.Float('Overtime Hours', compute='_compute_attendance_status', store=True)
    
    # Status Fields
    is_late = fields.Boolean('Is Late', compute='_compute_attendance_status', store=True)
    early_departure = fields.Boolean('Early Departure', compute='_compute_attendance_status', store=True)
    is_absent = fields.Boolean('Is Absent', compute='_compute_attendance_status', store=True)
    is_holiday = fields.Boolean('Is Holiday', default=False)
    
    # Additional Information
    manual_entry = fields.Boolean('Manual Entry', default=False)
    notes = fields.Text('Notes')
    
    # Status
    status = fields.Selection([
        ('present', 'Present'),
        ('late', 'Late'),
        ('absent', 'Absent'),
        ('early_departure', 'Early Departure'),
        ('holiday', 'Holiday')
    ], string='Status', compute='_compute_final_status', store=True)
    
    _sql_constraints = [
        ('unique_employee_date', 'unique(employee_id, attendance_date)', 
         'Only one attendance record per employee per day is allowed!'),
    ]
    
    @api.depends('check_in', 'check_out')
    def _compute_worked_hours(self):
        for record in self:
            if record.check_in and record.check_out:
                delta = record.check_out - record.check_in
                record.worked_hours = delta.total_seconds() / 3600
            else:
                record.worked_hours = 0
    
    @api.depends('check_in', 'check_out', 'employee_id.work_rule_id', 'attendance_date')
    def _compute_attendance_status(self):
        for record in self:
            work_rule = record.employee_id.work_rule_id
            if not work_rule:
                record.late_minutes = 0
                record.early_departure_minutes = 0
                record.overtime_hours = 0
                record.is_late = False
                record.early_departure = False
                record.is_absent = False
                continue
            
            # Check if it's a working day
            if not work_rule.is_working_day(record.attendance_date):
                record.late_minutes = 0
                record.early_departure_minutes = 0
                record.overtime_hours = 0
                record.is_late = False
                record.early_departure = False
                record.is_absent = False
                continue
            
            # Calculate late minutes
            if record.check_in:
                record.late_minutes = work_rule.calculate_late_minutes(record.check_in)
                record.is_late = record.late_minutes > 0
            else:
                record.late_minutes = 0
                record.is_late = False
            
            # Calculate early departure
            if record.check_out:
                record.early_departure_minutes = work_rule.calculate_early_departure_minutes(record.check_out)
                record.early_departure = record.early_departure_minutes > 0
            else:
                record.early_departure_minutes = 0
                record.early_departure = False
            
            # Calculate overtime
            if record.worked_hours:
                record.overtime_hours = work_rule.calculate_overtime_hours(record.worked_hours)
            else:
                record.overtime_hours = 0
            
            # Check absence
            if not record.check_in and work_rule.is_working_day(record.attendance_date):
                record.is_absent = True
            else:
                record.is_absent = False
    
    @api.depends('is_late', 'early_departure', 'is_absent', 'is_holiday', 'check_in')
    def _compute_final_status(self):
        for record in self:
            if record.is_holiday:
                record.status = 'holiday'
            elif record.is_absent:
                record.status = 'absent'
            elif record.is_late:
                record.status = 'late'
            elif record.early_departure:
                record.status = 'early_departure'
            elif record.check_in:
                record.status = 'present'
            else:
                record.status = 'absent'

    @api.constrains('check_in', 'check_out')
    def _check_times(self):
        for record in self:
            if record.check_in and record.check_out:
                if record.check_out <= record.check_in:
                    raise ValidationError(_('Check-out time must be after check-in time.'))

    def action_manual_checkout(self):
        """Manual check-out for this attendance record"""
        self.ensure_one()
        if not self.check_in:
            raise ValidationError(_('Cannot check-out without check-in.'))
        if self.check_out:
            raise ValidationError(_('Employee is already checked out.'))

        self.write({
            'check_out': fields.Datetime.now(),
            'manual_entry': True,
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Check-out Successful'),
                'message': _('Manual check-out recorded for %s') % self.employee_id.name,
                'type': 'success',
            }
        }

    @api.model
    def create_daily_attendance_records(self):
        """Create attendance records for all active employees for today"""
        today = fields.Date.today()
        employees = self.env['biometric.employee'].search([('active', '=', True)])

        for employee in employees:
            # Check if record already exists
            existing = self.search([
                ('employee_id', '=', employee.id),
                ('attendance_date', '=', today)
            ])

            if not existing and employee.work_rule_id:
                # Check if today is a working day
                if employee.work_rule_id.is_working_day(today):
                    self.create({
                        'employee_id': employee.id,
                        'device_id': employee.device_id.id,
                        'attendance_date': today,
                    })

        return True

    @api.model
    def mark_absent_employees(self):
        """Mark employees as absent if they haven't checked in after absence threshold"""
        today = fields.Date.today()
        current_time = fields.Datetime.now()

        # Get all today's attendance records without check-in
        attendance_records = self.search([
            ('attendance_date', '=', today),
            ('check_in', '=', False),
            ('is_holiday', '=', False)
        ])

        for record in attendance_records:
            work_rule = record.employee_id.work_rule_id
            if work_rule:
                # Calculate absence threshold time
                work_start_hour = int(work_rule.work_start_time)
                work_start_minute = int((work_rule.work_start_time - work_start_hour) * 60)

                absence_threshold = current_time.replace(
                    hour=work_start_hour,
                    minute=work_start_minute,
                    second=0,
                    microsecond=0
                ) + timedelta(hours=work_rule.absence_after)

                if current_time >= absence_threshold:
                    record.write({'is_absent': True})

        return True

    def action_view_employee(self):
        """View employee for this attendance record"""
        self.ensure_one()
        return {
            'name': _('Employee - %s') % self.employee_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.employee',
            'res_id': self.employee_id.id,
            'view_mode': 'form',
        }

    @api.model
    def get_dashboard_data(self):
        """Get data for biometric dashboard"""
        today = fields.Date.today()

        # Get today's attendance
        today_attendance = self.search([('attendance_date', '=', today)])

        # Calculate statistics
        total_employees = self.env['biometric.employee'].search_count([('active', '=', True)])
        present_today = len(today_attendance.filtered(lambda r: r.status == 'present'))
        late_today = len(today_attendance.filtered(lambda r: r.status == 'late'))
        absent_today = total_employees - len(today_attendance.filtered(lambda r: r.check_in))

        # Get weekly attendance data for chart
        weekly_data = self._get_weekly_attendance_data()

        # Get device status
        devices = self.env['biometric.device'].search([('active', '=', True)])
        device_data = []
        for device in devices:
            device_data.append({
                'name': device.name,
                'status': device.status,
                'total_employees': device.total_employees,
                'total_records': device.total_records,
            })

        # Get recent activity
        recent_activity = self._get_recent_activity()

        return {
            'total_employees': total_employees,
            'present_today': present_today,
            'late_today': late_today,
            'absent_today': absent_today,
            'status_summary': {
                'present': present_today,
                'late': late_today,
                'absent': absent_today,
            },
            'attendance_chart': weekly_data,
            'devices': device_data,
            'recent_activity': recent_activity,
        }

    def _get_weekly_attendance_data(self):
        """Get weekly attendance data for chart"""
        today = fields.Date.today()
        labels = []
        data = []

        for i in range(7):
            date = today - timedelta(days=6-i)
            labels.append(date.strftime('%a'))

            day_attendance = self.search_count([
                ('attendance_date', '=', date),
                ('check_in', '!=', False)
            ])
            data.append(day_attendance)

        return {
            'labels': labels,
            'data': data,
        }

    def _get_recent_activity(self):
        """Get recent attendance activity"""
        recent_records = self.search([
            ('check_in', '!=', False)
        ], order='check_in desc', limit=10)

        activity = []
        for record in recent_records:
            activity.append({
                'time': record.check_in.strftime('%H:%M') if record.check_in else '',
                'employee': record.employee_id.name,
                'action': 'Check-in',
                'device': record.device_id.name,
                'status': record.status,
            })

        return activity
