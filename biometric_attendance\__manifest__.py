# -*- coding: utf-8 -*-
{
    'name': 'Biometric Attendance System',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Complete biometric attendance system with fingerprint device integration',
    'description': """
Biometric Attendance System for Odoo 15
========================================

This module provides a complete biometric attendance system that integrates with fingerprint devices.

Key Features:
* Connect to fingerprint devices via IP or Serial Port
* Independent employee management from device data
* Automatic attendance calculation (late arrival, early departure, absence)
* Configurable work rules and policies
* Comprehensive reporting system
* Manual and scheduled data synchronization
* Complete user interface for device and attendance management

The module is completely independent and doesn't require hr_attendance or any other HR modules.
    """,
    'author': 'Custom Development',
    'website': '',
    'license': 'LGPL-3',
    'depends': ['base', 'web'],
    'data': [
        # Security
        'security/biometric_security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/biometric_data.xml',
        'data/ir_cron_simple.xml',
        
        # Views
        'views/biometric_device_views.xml',
        'views/biometric_attendance_views.xml',
        'views/biometric_employee_views.xml',
        'views/biometric_work_rule_views.xml',
        'views/biometric_config_views.xml',
        'views/res_config_settings_views.xml',
        'views/biometric_menu.xml',
        # 'views/biometric_report_views.xml',
        # 'views/biometric_report_wizard_views.xml',
        
        # Reports
        'reports/attendance_report.xml',
        'reports/late_report.xml',
        'reports/absence_report.xml',
    ],
    'demo': [
        'demo/biometric_demo.xml',
    ],
    'external_dependencies': {
        'python': ['pyserial', 'socket'],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
}
