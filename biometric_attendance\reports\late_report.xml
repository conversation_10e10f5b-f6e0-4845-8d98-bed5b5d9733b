<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Late Arrival Report Template -->
    <template id="report_late_arrival_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Late Arrival Report</h2>
                        <p>
                            <strong>Period:</strong> 
                            <span t-esc="date_from"/> to <span t-esc="date_to"/>
                        </p>
                    </div>
                </div>

                <table class="table table-sm o_main_table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Employee</th>
                            <th>Department</th>
                            <th>Expected Time</th>
                            <th>Actual Time</th>
                            <th>Late Minutes</th>
                            <th>Work Rule</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="docs" t-as="attendance">
                            <tr t-if="attendance.is_late">
                                <td><span t-field="attendance.attendance_date"/></td>
                                <td><span t-field="attendance.employee_id.name"/></td>
                                <td><span t-field="attendance.employee_id.department"/></td>
                                <td>
                                    <t t-if="attendance.employee_id.work_rule_id">
                                        <span t-esc="'{:02d}:{:02d}'.format(int(attendance.employee_id.work_rule_id.work_start_time), int((attendance.employee_id.work_rule_id.work_start_time % 1) * 60))"/>
                                    </t>
                                </td>
                                <td><span t-field="attendance.check_in" t-options="{'widget': 'time'}"/></td>
                                <td class="text-danger"><strong><span t-field="attendance.late_minutes"/> min</strong></td>
                                <td><span t-field="attendance.employee_id.work_rule_id.name"/></td>
                            </tr>
                        </t>
                    </tbody>
                </table>

                <!-- Summary Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Summary</h4>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Total Late Days</th>
                                    <th>Total Late Minutes</th>
                                    <th>Average Late Minutes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="late_summary" t-as="summary">
                                    <tr>
                                        <td><span t-esc="summary['employee']"/></td>
                                        <td><span t-esc="summary['late_days']"/></td>
                                        <td><span t-esc="summary['total_late_minutes']"/> min</td>
                                        <td><span t-esc="round(summary['avg_late_minutes'], 1)"/> min</td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Late Arrival Report Action -->
    <record id="action_report_late_arrival" model="ir.actions.report">
        <field name="name">Late Arrival Report</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_late_arrival_document</field>
        <field name="report_file">biometric_attendance.report_late_arrival_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Monthly Late Report Template -->
    <template id="report_monthly_late_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                
                <div class="row">
                    <div class="col-12">
                        <h2>Monthly Late Arrival Analysis</h2>
                        <p>
                            <strong>Month:</strong> 
                            <span t-esc="month_year"/>
                        </p>
                    </div>
                </div>

                <!-- Chart placeholder -->
                <div class="row">
                    <div class="col-12">
                        <h4>Late Arrival Trends</h4>
                        <div class="alert alert-info">
                            Chart showing daily late arrival counts would be displayed here in the web interface.
                        </div>
                    </div>
                </div>

                <!-- Top Late Employees -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Top Late Employees</h4>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Late Days</th>
                                    <th>Total Late Minutes</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="top_late_employees" t-as="employee_data">
                                    <tr>
                                        <td><span t-esc="employee_data_index + 1"/></td>
                                        <td><span t-esc="employee_data['employee']"/></td>
                                        <td><span t-esc="employee_data['department']"/></td>
                                        <td><span t-esc="employee_data['late_days']"/></td>
                                        <td><span t-esc="employee_data['total_late_minutes']"/> min</td>
                                        <td><span t-esc="round(employee_data['percentage'], 1)"/>%</td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Monthly Late Report Action -->
    <record id="action_report_monthly_late" model="ir.actions.report">
        <field name="name">Monthly Late Analysis</field>
        <field name="model">biometric.attendance</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">biometric_attendance.report_monthly_late_document</field>
        <field name="report_file">biometric_attendance.report_monthly_late_document</field>
        <field name="binding_model_id" ref="model_biometric_attendance"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
