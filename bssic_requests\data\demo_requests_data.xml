<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Employees -->
        <record id="demo_employee_1" model="hr.employee">
            <field name="name"><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_2" model="hr.employee">
            <field name="name"><PERSON><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_3" model="hr.employee">
            <field name="name"><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_4" model="hr.employee">
            <field name="name"><PERSON><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_5" model="hr.employee">
            <field name="name">Khalid Mahmoud</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_6" model="hr.employee">
            <field name="name">Nour Abdullah</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_7" model="hr.employee">
            <field name="name">Youssef Ahmad</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_8" model="hr.employee">
            <field name="name">Layla Saleh</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_9" model="hr.employee">
            <field name="name">Hassan Nasser</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_10" model="hr.employee">
            <field name="name">Aisha Farouk</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_11" model="hr.employee">
            <field name="name">Mohamed Saeed</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_12" model="hr.employee">
            <field name="name">Zeinab Karim</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_13" model="hr.employee">
            <field name="name">Ali Rashid</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_14" model="hr.employee">
            <field name="name">Huda Mansour</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_15" model="hr.employee">
            <field name="name">Tariq Othman</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_16" model="hr.employee">
            <field name="name">Rana Khalil</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_17" model="hr.employee">
            <field name="name">Sami Habib</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_18" model="hr.employee">
            <field name="name">Dina Fouad</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_19" model="hr.employee">
            <field name="name">Amr Zaki</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        <record id="demo_employee_20" model="hr.employee">
            <field name="name">Lina Badawi</field>
            <field name="work_email"><EMAIL></field>
            <field name="employee_type">employee</field>
        </record>
        
        <!-- Demo Requests -->
        
        <!-- Password Reset Requests -->
        <record id="demo_password_reset_1" model="bssic.request">
            <field name="name">Password Reset - REQ001</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">hassan.nasser</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 50 days ago</field>
        </record>
        <record id="demo_password_reset_2" model="bssic.request">
            <field name="name">Password Reset - REQ002</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">zeinab.karim</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with direct_manager status</field>
        </record>
        <record id="demo_password_reset_3" model="bssic.request">
            <field name="name">Password Reset - REQ003</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">dina.fouad</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_4" model="bssic.request">
            <field name="name">Password Reset - REQ004</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">khalid.mahmoud</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 57 days ago</field>
        </record>
        <record id="demo_password_reset_5" model="bssic.request">
            <field name="name">Password Reset - REQ005</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">zeinab.karim</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_6" model="bssic.request">
            <field name="name">Password Reset - REQ006</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=34)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 34 days ago</field>
        </record>
        <record id="demo_password_reset_7" model="bssic.request">
            <field name="name">Password Reset - REQ007</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">zeinab.karim</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_8" model="bssic.request">
            <field name="name">Password Reset - REQ008</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">ali.rashid</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_9" model="bssic.request">
            <field name="name">Password Reset - REQ009</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">layla.saleh</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with draft status</field>
        </record>
        <record id="demo_password_reset_10" model="bssic.request">
            <field name="name">Password Reset - REQ010</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="username">dina.fouad</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with in_progress status</field>
        </record>
        <record id="demo_password_reset_11" model="bssic.request">
            <field name="name">Password Reset - REQ011</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_12" model="bssic.request">
            <field name="name">Password Reset - REQ012</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with in_progress status</field>
        </record>
        <record id="demo_password_reset_13" model="bssic.request">
            <field name="name">Password Reset - REQ013</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">fatima.ali</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_14" model="bssic.request">
            <field name="name">Password Reset - REQ014</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 42 days ago</field>
        </record>
        <record id="demo_password_reset_15" model="bssic.request">
            <field name="name">Password Reset - REQ015</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 60 days ago</field>
        </record>
        <record id="demo_password_reset_16" model="bssic.request">
            <field name="name">Password Reset - REQ016</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">mohamed.saeed</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_17" model="bssic.request">
            <field name="name">Password Reset - REQ017</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">layla.saleh</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_18" model="bssic.request">
            <field name="name">Password Reset - REQ018</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 29 days ago</field>
        </record>
        <record id="demo_password_reset_19" model="bssic.request">
            <field name="name">Password Reset - REQ019</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="username">mohamed.saeed</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_20" model="bssic.request">
            <field name="name">Password Reset - REQ020</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">ahmed.mohamed</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_21" model="bssic.request">
            <field name="name">Password Reset - REQ021</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="username">maryam.ibrahim</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_22" model="bssic.request">
            <field name="name">Password Reset - REQ022</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">huda.mansour</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with audit_manager status</field>
        </record>
        <record id="demo_password_reset_23" model="bssic.request">
            <field name="name">Password Reset - REQ023</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_24" model="bssic.request">
            <field name="name">Password Reset - REQ024</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="username">maryam.ibrahim</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 5 days ago</field>
        </record>
        <record id="demo_password_reset_25" model="bssic.request">
            <field name="name">Password Reset - REQ025</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with it_manager status</field>
        </record>
        <record id="demo_password_reset_26" model="bssic.request">
            <field name="name">Password Reset - REQ026</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">amr.zaki</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with it_manager status</field>
        </record>
        <record id="demo_password_reset_27" model="bssic.request">
            <field name="name">Password Reset - REQ027</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">maryam.ibrahim</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 54 days ago</field>
        </record>
        <record id="demo_password_reset_28" model="bssic.request">
            <field name="name">Password Reset - REQ028</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 26 days ago</field>
        </record>
        <record id="demo_password_reset_29" model="bssic.request">
            <field name="name">Password Reset - REQ029</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="username">tariq.othman</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 29 days ago</field>
        </record>
        <record id="demo_password_reset_30" model="bssic.request">
            <field name="name">Password Reset - REQ030</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">ali.rashid</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 22 days ago</field>
        </record>
        <record id="demo_password_reset_31" model="bssic.request">
            <field name="name">Password Reset - REQ031</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">amr.zaki</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 58 days ago</field>
        </record>
        <record id="demo_password_reset_32" model="bssic.request">
            <field name="name">Password Reset - REQ032</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">other</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_33" model="bssic.request">
            <field name="name">Password Reset - REQ033</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_34" model="bssic.request">
            <field name="name">Password Reset - REQ034</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">rana.khalil</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with audit_manager status</field>
        </record>
        <record id="demo_password_reset_35" model="bssic.request">
            <field name="name">Password Reset - REQ035</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">hassan.nasser</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_36" model="bssic.request">
            <field name="name">Password Reset - REQ036</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_37" model="bssic.request">
            <field name="name">Password Reset - REQ037</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">lina.badawi</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 36 days ago</field>
        </record>
        <record id="demo_password_reset_38" model="bssic.request">
            <field name="name">Password Reset - REQ038</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="username">amr.zaki</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_39" model="bssic.request">
            <field name="name">Password Reset - REQ039</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">amr.zaki</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with audit_manager status</field>
        </record>
        <record id="demo_password_reset_40" model="bssic.request">
            <field name="name">Password Reset - REQ040</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">ahmed.mohamed</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_41" model="bssic.request">
            <field name="name">Password Reset - REQ041</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">ali.rashid</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_42" model="bssic.request">
            <field name="name">Password Reset - REQ042</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">zeinab.karim</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_43" model="bssic.request">
            <field name="name">Password Reset - REQ043</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">fatima.ali</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with audit_manager status</field>
        </record>
        <record id="demo_password_reset_44" model="bssic.request">
            <field name="name">Password Reset - REQ044</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">ahmed.mohamed</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with submitted status</field>
        </record>
        <record id="demo_password_reset_45" model="bssic.request">
            <field name="name">Password Reset - REQ045</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="username">fatima.ali</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_46" model="bssic.request">
            <field name="name">Password Reset - REQ046</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">ahmed.mohamed</field>
            <field name="device_type">system</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Test Password Reset created 24 days ago</field>
        </record>
        <record id="demo_password_reset_47" model="bssic.request">
            <field name="name">Password Reset - REQ047</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_48" model="bssic.request">
            <field name="name">Password Reset - REQ048</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_49" model="bssic.request">
            <field name="name">Password Reset - REQ049</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_50" model="bssic.request">
            <field name="name">Password Reset - REQ050</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_51" model="bssic.request">
            <field name="name">Password Reset - REQ051</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">omar.hassan</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with submitted status</field>
        </record>
        <record id="demo_password_reset_52" model="bssic.request">
            <field name="name">Password Reset - REQ052</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">zeinab.karim</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with submitted status</field>
        </record>
        <record id="demo_password_reset_53" model="bssic.request">
            <field name="name">Password Reset - REQ053</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="username">nour.abdullah</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_54" model="bssic.request">
            <field name="name">Password Reset - REQ054</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">dina.fouad</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 26 days ago</field>
        </record>
        <record id="demo_password_reset_55" model="bssic.request">
            <field name="name">Password Reset - REQ055</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">mohamed.saeed</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with direct_manager status</field>
        </record>
        <record id="demo_password_reset_56" model="bssic.request">
            <field name="name">Password Reset - REQ056</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">rana.khalil</field>
            <field name="device_type">system</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Sample Password Reset with direct_manager status</field>
        </record>
        <record id="demo_password_reset_57" model="bssic.request">
            <field name="name">Password Reset - REQ057</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">mohamed.saeed</field>
            <field name="device_type">internet</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_58" model="bssic.request">
            <field name="name">Password Reset - REQ058</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">layla.saleh</field>
            <field name="device_type">swift</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo data for Password Reset workflow testing</field>
        </record>
        <record id="demo_password_reset_59" model="bssic.request">
            <field name="name">Password Reset - REQ059</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="username">ali.rashid</field>
            <field name="device_type">swift</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        <record id="demo_password_reset_60" model="bssic.request">
            <field name="name">Password Reset - REQ060</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="username">aisha.farouk</field>
            <field name="device_type">internet</field>
            <field name="request_reason">password_reset</field>
            <field name="description">Sample Password Reset with direct_manager status</field>
        </record>
        <record id="demo_password_reset_61" model="bssic.request">
            <field name="name">Password Reset - REQ061</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="username">amr.zaki</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Test Password Reset created 48 days ago</field>
        </record>
        <record id="demo_password_reset_62" model="bssic.request">
            <field name="name">Password Reset - REQ062</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_password_reset"/>
            <field name="request_type_code">password_reset</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="username">ali.rashid</field>
            <field name="device_type">other</field>
            <field name="request_reason">account_reactivation</field>
            <field name="description">Demo Password Reset for testing purposes</field>
        </record>
        
        <!-- USB Access Requests -->
        <record id="demo_usb_63" model="bssic.request">
            <field name="name">USB Access - REQ063</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Software</field>
            <field name="description">Test USB Access created 41 days ago</field>
        </record>
        <record id="demo_usb_64" model="bssic.request">
            <field name="name">USB Access - REQ064</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_65" model="bssic.request">
            <field name="name">USB Access - REQ065</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Sample USB Access with direct_manager status</field>
        </record>
        <record id="demo_usb_66" model="bssic.request">
            <field name="name">USB Access - REQ066</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_67" model="bssic.request">
            <field name="name">USB Access - REQ067</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Software</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_68" model="bssic.request">
            <field name="name">USB Access - REQ068</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Sample USB Access with in_progress status</field>
        </record>
        <record id="demo_usb_69" model="bssic.request">
            <field name="name">USB Access - REQ069</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_70" model="bssic.request">
            <field name="name">USB Access - REQ070</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_71" model="bssic.request">
            <field name="name">USB Access - REQ071</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_72" model="bssic.request">
            <field name="name">USB Access - REQ072</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Documents</field>
            <field name="description">Test USB Access created 25 days ago</field>
        </record>
        <record id="demo_usb_73" model="bssic.request">
            <field name="name">USB Access - REQ073</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_74" model="bssic.request">
            <field name="name">USB Access - REQ074</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_75" model="bssic.request">
            <field name="name">USB Access - REQ075</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Documents</field>
            <field name="description">Test USB Access created 15 days ago</field>
        </record>
        <record id="demo_usb_76" model="bssic.request">
            <field name="name">USB Access - REQ076</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Documents</field>
            <field name="description">Test USB Access created 13 days ago</field>
        </record>
        <record id="demo_usb_77" model="bssic.request">
            <field name="name">USB Access - REQ077</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Documents</field>
            <field name="description">Sample USB Access with completed status</field>
        </record>
        <record id="demo_usb_78" model="bssic.request">
            <field name="name">USB Access - REQ078</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_79" model="bssic.request">
            <field name="name">USB Access - REQ079</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Sample USB Access with submitted status</field>
        </record>
        <record id="demo_usb_80" model="bssic.request">
            <field name="name">USB Access - REQ080</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Software</field>
            <field name="description">Test USB Access created 57 days ago</field>
        </record>
        <record id="demo_usb_81" model="bssic.request">
            <field name="name">USB Access - REQ081</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Sample USB Access with submitted status</field>
        </record>
        <record id="demo_usb_82" model="bssic.request">
            <field name="name">USB Access - REQ082</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_83" model="bssic.request">
            <field name="name">USB Access - REQ083</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_84" model="bssic.request">
            <field name="name">USB Access - REQ084</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Sample USB Access with rejected status</field>
        </record>
        <record id="demo_usb_85" model="bssic.request">
            <field name="name">USB Access - REQ085</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Documents</field>
            <field name="description">Test USB Access created 60 days ago</field>
        </record>
        <record id="demo_usb_86" model="bssic.request">
            <field name="name">USB Access - REQ086</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_87" model="bssic.request">
            <field name="name">USB Access - REQ087</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_88" model="bssic.request">
            <field name="name">USB Access - REQ088</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_89" model="bssic.request">
            <field name="name">USB Access - REQ089</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 50 days ago</field>
        </record>
        <record id="demo_usb_90" model="bssic.request">
            <field name="name">USB Access - REQ090</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_91" model="bssic.request">
            <field name="name">USB Access - REQ091</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_92" model="bssic.request">
            <field name="name">USB Access - REQ092</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_93" model="bssic.request">
            <field name="name">USB Access - REQ093</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_94" model="bssic.request">
            <field name="name">USB Access - REQ094</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_95" model="bssic.request">
            <field name="name">USB Access - REQ095</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Documents</field>
            <field name="description">Sample USB Access with draft status</field>
        </record>
        <record id="demo_usb_96" model="bssic.request">
            <field name="name">USB Access - REQ096</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_97" model="bssic.request">
            <field name="name">USB Access - REQ097</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Test USB Access created 31 days ago</field>
        </record>
        <record id="demo_usb_98" model="bssic.request">
            <field name="name">USB Access - REQ098</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Test USB Access created 60 days ago</field>
        </record>
        <record id="demo_usb_99" model="bssic.request">
            <field name="name">USB Access - REQ099</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Test USB Access created 16 days ago</field>
        </record>
        <record id="demo_usb_100" model="bssic.request">
            <field name="name">USB Access - REQ100</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 60 days ago</field>
        </record>
        <record id="demo_usb_101" model="bssic.request">
            <field name="name">USB Access - REQ101</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_102" model="bssic.request">
            <field name="name">USB Access - REQ102</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_103" model="bssic.request">
            <field name="name">USB Access - REQ103</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_104" model="bssic.request">
            <field name="name">USB Access - REQ104</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Training materials</field>
            <field name="description">Sample USB Access with submitted status</field>
        </record>
        <record id="demo_usb_105" model="bssic.request">
            <field name="name">USB Access - REQ105</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 28 days ago</field>
        </record>
        <record id="demo_usb_106" model="bssic.request">
            <field name="name">USB Access - REQ106</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 11 days ago</field>
        </record>
        <record id="demo_usb_107" model="bssic.request">
            <field name="name">USB Access - REQ107</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_108" model="bssic.request">
            <field name="name">USB Access - REQ108</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Software</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_109" model="bssic.request">
            <field name="name">USB Access - REQ109</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Software</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_110" model="bssic.request">
            <field name="name">USB Access - REQ110</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Sample USB Access with audit_manager status</field>
        </record>
        <record id="demo_usb_111" model="bssic.request">
            <field name="name">USB Access - REQ111</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Training materials</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 58 days ago</field>
        </record>
        <record id="demo_usb_112" model="bssic.request">
            <field name="name">USB Access - REQ112</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_113" model="bssic.request">
            <field name="name">USB Access - REQ113</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_114" model="bssic.request">
            <field name="name">USB Access - REQ114</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Training materials</field>
            <field name="description">Sample USB Access with draft status</field>
        </record>
        <record id="demo_usb_115" model="bssic.request">
            <field name="name">USB Access - REQ115</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Documents</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_116" model="bssic.request">
            <field name="name">USB Access - REQ116</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 58 days ago</field>
        </record>
        <record id="demo_usb_117" model="bssic.request">
            <field name="name">USB Access - REQ117</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Training materials</field>
            <field name="description">Test USB Access created 43 days ago</field>
        </record>
        <record id="demo_usb_118" model="bssic.request">
            <field name="name">USB Access - REQ118</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Backup files</field>
            <field name="usb_duration">1 day</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_119" model="bssic.request">
            <field name="name">USB Access - REQ119</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Documents</field>
            <field name="description">Test USB Access created 7 days ago</field>
        </record>
        <record id="demo_usb_120" model="bssic.request">
            <field name="name">USB Access - REQ120</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 hours</field>
            <field name="data_type">Software</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_121" model="bssic.request">
            <field name="name">USB Access - REQ121</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">1 hour</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        <record id="demo_usb_122" model="bssic.request">
            <field name="name">USB Access - REQ122</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="usb_purpose">Software installation</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Training materials</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_123" model="bssic.request">
            <field name="name">USB Access - REQ123</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">1 week</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo data for USB Access workflow testing</field>
        </record>
        <record id="demo_usb_124" model="bssic.request">
            <field name="name">USB Access - REQ124</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_usb"/>
            <field name="request_type_code">usb</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="usb_purpose">Data transfer</field>
            <field name="usb_duration">3 days</field>
            <field name="data_type">Financial reports</field>
            <field name="description">Demo USB Access for testing purposes</field>
        </record>
        
        <!-- Extension Request Requests -->
        <record id="demo_extension_125" model="bssic.request">
            <field name="name">Extension Request - REQ125</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_126" model="bssic.request">
            <field name="name">Extension Request - REQ126</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with submitted status</field>
        </record>
        <record id="demo_extension_127" model="bssic.request">
            <field name="name">Extension Request - REQ127</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Sample Extension Request with submitted status</field>
        </record>
        <record id="demo_extension_128" model="bssic.request">
            <field name="name">Extension Request - REQ128</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_129" model="bssic.request">
            <field name="name">Extension Request - REQ129</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_130" model="bssic.request">
            <field name="name">Extension Request - REQ130</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Sample Extension Request with draft status</field>
        </record>
        <record id="demo_extension_131" model="bssic.request">
            <field name="name">Extension Request - REQ131</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Sample Extension Request with completed status</field>
        </record>
        <record id="demo_extension_132" model="bssic.request">
            <field name="name">Extension Request - REQ132</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Test Extension Request created 16 days ago</field>
        </record>
        <record id="demo_extension_133" model="bssic.request">
            <field name="name">Extension Request - REQ133</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Sample Extension Request with direct_manager status</field>
        </record>
        <record id="demo_extension_134" model="bssic.request">
            <field name="name">Extension Request - REQ134</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with it_manager status</field>
        </record>
        <record id="demo_extension_135" model="bssic.request">
            <field name="name">Extension Request - REQ135</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with assigned status</field>
        </record>
        <record id="demo_extension_136" model="bssic.request">
            <field name="name">Extension Request - REQ136</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_137" model="bssic.request">
            <field name="name">Extension Request - REQ137</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_138" model="bssic.request">
            <field name="name">Extension Request - REQ138</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Test Extension Request created 31 days ago</field>
        </record>
        <record id="demo_extension_139" model="bssic.request">
            <field name="name">Extension Request - REQ139</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Test Extension Request created 22 days ago</field>
        </record>
        <record id="demo_extension_140" model="bssic.request">
            <field name="name">Extension Request - REQ140</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_141" model="bssic.request">
            <field name="name">Extension Request - REQ141</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_142" model="bssic.request">
            <field name="name">Extension Request - REQ142</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_143" model="bssic.request">
            <field name="name">Extension Request - REQ143</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Sample Extension Request with in_progress status</field>
        </record>
        <record id="demo_extension_144" model="bssic.request">
            <field name="name">Extension Request - REQ144</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Sample Extension Request with submitted status</field>
        </record>
        <record id="demo_extension_145" model="bssic.request">
            <field name="name">Extension Request - REQ145</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with submitted status</field>
        </record>
        <record id="demo_extension_146" model="bssic.request">
            <field name="name">Extension Request - REQ146</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Test Extension Request created 44 days ago</field>
        </record>
        <record id="demo_extension_147" model="bssic.request">
            <field name="name">Extension Request - REQ147</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Test Extension Request created 29 days ago</field>
        </record>
        <record id="demo_extension_148" model="bssic.request">
            <field name="name">Extension Request - REQ148</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Test Extension Request created 29 days ago</field>
        </record>
        <record id="demo_extension_149" model="bssic.request">
            <field name="name">Extension Request - REQ149</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_150" model="bssic.request">
            <field name="name">Extension Request - REQ150</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_151" model="bssic.request">
            <field name="name">Extension Request - REQ151</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Sample Extension Request with direct_manager status</field>
        </record>
        <record id="demo_extension_152" model="bssic.request">
            <field name="name">Extension Request - REQ152</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Test Extension Request created 6 days ago</field>
        </record>
        <record id="demo_extension_153" model="bssic.request">
            <field name="name">Extension Request - REQ153</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_154" model="bssic.request">
            <field name="name">Extension Request - REQ154</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_155" model="bssic.request">
            <field name="name">Extension Request - REQ155</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_156" model="bssic.request">
            <field name="name">Extension Request - REQ156</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_157" model="bssic.request">
            <field name="name">Extension Request - REQ157</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Sample Extension Request with draft status</field>
        </record>
        <record id="demo_extension_158" model="bssic.request">
            <field name="name">Extension Request - REQ158</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_159" model="bssic.request">
            <field name="name">Extension Request - REQ159</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Test Extension Request created 51 days ago</field>
        </record>
        <record id="demo_extension_160" model="bssic.request">
            <field name="name">Extension Request - REQ160</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_161" model="bssic.request">
            <field name="name">Extension Request - REQ161</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Test Extension Request created 42 days ago</field>
        </record>
        <record id="demo_extension_162" model="bssic.request">
            <field name="name">Extension Request - REQ162</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_163" model="bssic.request">
            <field name="name">Extension Request - REQ163</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_164" model="bssic.request">
            <field name="name">Extension Request - REQ164</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Test Extension Request created 59 days ago</field>
        </record>
        <record id="demo_extension_165" model="bssic.request">
            <field name="name">Extension Request - REQ165</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Sample Extension Request with it_manager status</field>
        </record>
        <record id="demo_extension_166" model="bssic.request">
            <field name="name">Extension Request - REQ166</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Test Extension Request created 29 days ago</field>
        </record>
        <record id="demo_extension_167" model="bssic.request">
            <field name="name">Extension Request - REQ167</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_168" model="bssic.request">
            <field name="name">Extension Request - REQ168</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_169" model="bssic.request">
            <field name="name">Extension Request - REQ169</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_170" model="bssic.request">
            <field name="name">Extension Request - REQ170</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Sample Extension Request with draft status</field>
        </record>
        <record id="demo_extension_171" model="bssic.request">
            <field name="name">Extension Request - REQ171</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_172" model="bssic.request">
            <field name="name">Extension Request - REQ172</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_173" model="bssic.request">
            <field name="name">Extension Request - REQ173</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with audit_manager status</field>
        </record>
        <record id="demo_extension_174" model="bssic.request">
            <field name="name">Extension Request - REQ174</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Test Extension Request created 17 days ago</field>
        </record>
        <record id="demo_extension_175" model="bssic.request">
            <field name="name">Extension Request - REQ175</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_176" model="bssic.request">
            <field name="name">Extension Request - REQ176</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_177" model="bssic.request">
            <field name="name">Extension Request - REQ177</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Vendor delays</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_178" model="bssic.request">
            <field name="name">Extension Request - REQ178</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">1 week</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Test Extension Request created 47 days ago</field>
        </record>
        <record id="demo_extension_179" model="bssic.request">
            <field name="name">Extension Request - REQ179</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo data for Extension Request workflow testing</field>
        </record>
        <record id="demo_extension_180" model="bssic.request">
            <field name="name">Extension Request - REQ180</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_181" model="bssic.request">
            <field name="name">Extension Request - REQ181</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Test Extension Request created 25 days ago</field>
        </record>
        <record id="demo_extension_182" model="bssic.request">
            <field name="name">Extension Request - REQ182</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_183" model="bssic.request">
            <field name="name">Extension Request - REQ183</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="extension_duration">2 weeks</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Sample Extension Request with direct_manager status</field>
        </record>
        <record id="demo_extension_184" model="bssic.request">
            <field name="name">Extension Request - REQ184</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Resource constraints</field>
            <field name="description">Demo Extension Request for testing purposes</field>
        </record>
        <record id="demo_extension_185" model="bssic.request">
            <field name="name">Extension Request - REQ185</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">1 month</field>
            <field name="extension_reason">Additional requirements</field>
            <field name="description">Sample Extension Request with audit_manager status</field>
        </record>
        <record id="demo_extension_186" model="bssic.request">
            <field name="name">Extension Request - REQ186</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_extension"/>
            <field name="request_type_code">extension</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="extension_duration">3 days</field>
            <field name="extension_reason">Testing issues</field>
            <field name="description">Sample Extension Request with audit_manager status</field>
        </record>
        
        <!-- Email Request Requests -->
        <record id="demo_email_187" model="bssic.request">
            <field name="name">Email Request - REQ187</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 17 days ago</field>
        </record>
        <record id="demo_email_188" model="bssic.request">
            <field name="name">Email Request - REQ188</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_189" model="bssic.request">
            <field name="name">Email Request - REQ189</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_190" model="bssic.request">
            <field name="name">Email Request - REQ190</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Sample Email Request with completed status</field>
        </record>
        <record id="demo_email_191" model="bssic.request">
            <field name="name">Email Request - REQ191</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 35 days ago</field>
        </record>
        <record id="demo_email_192" model="bssic.request">
            <field name="name">Email Request - REQ192</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Sample Email Request with submitted status</field>
        </record>
        <record id="demo_email_193" model="bssic.request">
            <field name="name">Email Request - REQ193</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with audit_manager status</field>
        </record>
        <record id="demo_email_194" model="bssic.request">
            <field name="name">Email Request - REQ194</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_195" model="bssic.request">
            <field name="name">Email Request - REQ195</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_196" model="bssic.request">
            <field name="name">Email Request - REQ196</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Test Email Request created 5 days ago</field>
        </record>
        <record id="demo_email_197" model="bssic.request">
            <field name="name">Email Request - REQ197</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_198" model="bssic.request">
            <field name="name">Email Request - REQ198</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 20 days ago</field>
        </record>
        <record id="demo_email_199" model="bssic.request">
            <field name="name">Email Request - REQ199</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Sample Email Request with in_progress status</field>
        </record>
        <record id="demo_email_200" model="bssic.request">
            <field name="name">Email Request - REQ200</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Sample Email Request with direct_manager status</field>
        </record>
        <record id="demo_email_201" model="bssic.request">
            <field name="name">Email Request - REQ201</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_202" model="bssic.request">
            <field name="name">Email Request - REQ202</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with submitted status</field>
        </record>
        <record id="demo_email_203" model="bssic.request">
            <field name="name">Email Request - REQ203</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">New employee</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with direct_manager status</field>
        </record>
        <record id="demo_email_204" model="bssic.request">
            <field name="name">Email Request - REQ204</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_205" model="bssic.request">
            <field name="name">Email Request - REQ205</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Sample Email Request with assigned status</field>
        </record>
        <record id="demo_email_206" model="bssic.request">
            <field name="name">Email Request - REQ206</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Sample Email Request with direct_manager status</field>
        </record>
        <record id="demo_email_207" model="bssic.request">
            <field name="name">Email Request - REQ207</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Test Email Request created 46 days ago</field>
        </record>
        <record id="demo_email_208" model="bssic.request">
            <field name="name">Email Request - REQ208</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_209" model="bssic.request">
            <field name="name">Email Request - REQ209</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Test Email Request created 9 days ago</field>
        </record>
        <record id="demo_email_210" model="bssic.request">
            <field name="name">Email Request - REQ210</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_211" model="bssic.request">
            <field name="name">Email Request - REQ211</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Sample Email Request with it_manager status</field>
        </record>
        <record id="demo_email_212" model="bssic.request">
            <field name="name">Email Request - REQ212</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with audit_manager status</field>
        </record>
        <record id="demo_email_213" model="bssic.request">
            <field name="name">Email Request - REQ213</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Sample Email Request with it_manager status</field>
        </record>
        <record id="demo_email_214" model="bssic.request">
            <field name="name">Email Request - REQ214</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Sample Email Request with it_manager status</field>
        </record>
        <record id="demo_email_215" model="bssic.request">
            <field name="name">Email Request - REQ215</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Sample Email Request with direct_manager status</field>
        </record>
        <record id="demo_email_216" model="bssic.request">
            <field name="name">Email Request - REQ216</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 53 days ago</field>
        </record>
        <record id="demo_email_217" model="bssic.request">
            <field name="name">Email Request - REQ217</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 53 days ago</field>
        </record>
        <record id="demo_email_218" model="bssic.request">
            <field name="name">Email Request - REQ218</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">New employee</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 6 days ago</field>
        </record>
        <record id="demo_email_219" model="bssic.request">
            <field name="name">Email Request - REQ219</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Test Email Request created 57 days ago</field>
        </record>
        <record id="demo_email_220" model="bssic.request">
            <field name="name">Email Request - REQ220</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 30 days ago</field>
        </record>
        <record id="demo_email_221" model="bssic.request">
            <field name="name">Email Request - REQ221</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with direct_manager status</field>
        </record>
        <record id="demo_email_222" model="bssic.request">
            <field name="name">Email Request - REQ222</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_223" model="bssic.request">
            <field name="name">Email Request - REQ223</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_224" model="bssic.request">
            <field name="name">Email Request - REQ224</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_225" model="bssic.request">
            <field name="name">Email Request - REQ225</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Test Email Request created 13 days ago</field>
        </record>
        <record id="demo_email_226" model="bssic.request">
            <field name="name">Email Request - REQ226</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_227" model="bssic.request">
            <field name="name">Email Request - REQ227</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_228" model="bssic.request">
            <field name="name">Email Request - REQ228</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_229" model="bssic.request">
            <field name="name">Email Request - REQ229</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_230" model="bssic.request">
            <field name="name">Email Request - REQ230</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">new</field>
            <field name="email_reason">New employee</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_231" model="bssic.request">
            <field name="name">Email Request - REQ231</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Sample Email Request with draft status</field>
        </record>
        <record id="demo_email_232" model="bssic.request">
            <field name="name">Email Request - REQ232</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with draft status</field>
        </record>
        <record id="demo_email_233" model="bssic.request">
            <field name="name">Email Request - REQ233</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Test Email Request created 51 days ago</field>
        </record>
        <record id="demo_email_234" model="bssic.request">
            <field name="name">Email Request - REQ234</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_235" model="bssic.request">
            <field name="name">Email Request - REQ235</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Test Email Request created 47 days ago</field>
        </record>
        <record id="demo_email_236" model="bssic.request">
            <field name="name">Email Request - REQ236</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_237" model="bssic.request">
            <field name="name">Email Request - REQ237</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">password_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_238" model="bssic.request">
            <field name="name">Email Request - REQ238</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">New employee</field>
            <field name="description">Test Email Request created 12 days ago</field>
        </record>
        <record id="demo_email_239" model="bssic.request">
            <field name="name">Email Request - REQ239</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Forgot password</field>
            <field name="description">Test Email Request created 49 days ago</field>
        </record>
        <record id="demo_email_240" model="bssic.request">
            <field name="name">Email Request - REQ240</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">New employee</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_241" model="bssic.request">
            <field name="name">Email Request - REQ241</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_242" model="bssic.request">
            <field name="name">Email Request - REQ242</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">New employee</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_243" model="bssic.request">
            <field name="name">Email Request - REQ243</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=34)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">new</field>
            <field name="email_reason">Department change</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Test Email Request created 34 days ago</field>
        </record>
        <record id="demo_email_244" model="bssic.request">
            <field name="name">Email Request - REQ244</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_245" model="bssic.request">
            <field name="name">Email Request - REQ245</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Department change</field>
            <field name="description">Demo data for Email Request workflow testing</field>
        </record>
        <record id="demo_email_246" model="bssic.request">
            <field name="name">Email Request - REQ246</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="email_type">2fa_reset</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="description">Test Email Request created 58 days ago</field>
        </record>
        <record id="demo_email_247" model="bssic.request">
            <field name="name">Email Request - REQ247</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="email_type">new</field>
            <field name="email_reason">Forgot password</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Demo Email Request for testing purposes</field>
        </record>
        <record id="demo_email_248" model="bssic.request">
            <field name="name">Email Request - REQ248</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_email"/>
            <field name="request_type_code">email</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="email_type">new</field>
            <field name="email_reason">Lost 2FA device</field>
            <field name="email_agreement_accepted" eval="True"/>
            <field name="description">Sample Email Request with completed status</field>
        </record>
        
        <!-- Authorization Delegation Requests -->
        <record id="demo_authorization_delegation_249" model="bssic.request">
            <field name="name">Authorization Delegation - REQ249</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 56 days ago</field>
        </record>
        <record id="demo_authorization_delegation_250" model="bssic.request">
            <field name="name">Authorization Delegation - REQ250</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with assigned status</field>
        </record>
        <record id="demo_authorization_delegation_251" model="bssic.request">
            <field name="name">Authorization Delegation - REQ251</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 20 days ago</field>
        </record>
        <record id="demo_authorization_delegation_252" model="bssic.request">
            <field name="name">Authorization Delegation - REQ252</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_253" model="bssic.request">
            <field name="name">Authorization Delegation - REQ253</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_254" model="bssic.request">
            <field name="name">Authorization Delegation - REQ254</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 43 days ago</field>
        </record>
        <record id="demo_authorization_delegation_255" model="bssic.request">
            <field name="name">Authorization Delegation - REQ255</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_256" model="bssic.request">
            <field name="name">Authorization Delegation - REQ256</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_257" model="bssic.request">
            <field name="name">Authorization Delegation - REQ257</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 50 days ago</field>
        </record>
        <record id="demo_authorization_delegation_258" model="bssic.request">
            <field name="name">Authorization Delegation - REQ258</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_259" model="bssic.request">
            <field name="name">Authorization Delegation - REQ259</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with audit_manager status</field>
        </record>
        <record id="demo_authorization_delegation_260" model="bssic.request">
            <field name="name">Authorization Delegation - REQ260</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_261" model="bssic.request">
            <field name="name">Authorization Delegation - REQ261</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with in_progress status</field>
        </record>
        <record id="demo_authorization_delegation_262" model="bssic.request">
            <field name="name">Authorization Delegation - REQ262</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with in_progress status</field>
        </record>
        <record id="demo_authorization_delegation_263" model="bssic.request">
            <field name="name">Authorization Delegation - REQ263</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_264" model="bssic.request">
            <field name="name">Authorization Delegation - REQ264</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_265" model="bssic.request">
            <field name="name">Authorization Delegation - REQ265</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=34)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_266" model="bssic.request">
            <field name="name">Authorization Delegation - REQ266</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_267" model="bssic.request">
            <field name="name">Authorization Delegation - REQ267</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_268" model="bssic.request">
            <field name="name">Authorization Delegation - REQ268</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with direct_manager status</field>
        </record>
        <record id="demo_authorization_delegation_269" model="bssic.request">
            <field name="name">Authorization Delegation - REQ269</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_270" model="bssic.request">
            <field name="name">Authorization Delegation - REQ270</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_271" model="bssic.request">
            <field name="name">Authorization Delegation - REQ271</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_272" model="bssic.request">
            <field name="name">Authorization Delegation - REQ272</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_273" model="bssic.request">
            <field name="name">Authorization Delegation - REQ273</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with it_manager status</field>
        </record>
        <record id="demo_authorization_delegation_274" model="bssic.request">
            <field name="name">Authorization Delegation - REQ274</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with audit_manager status</field>
        </record>
        <record id="demo_authorization_delegation_275" model="bssic.request">
            <field name="name">Authorization Delegation - REQ275</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_276" model="bssic.request">
            <field name="name">Authorization Delegation - REQ276</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_277" model="bssic.request">
            <field name="name">Authorization Delegation - REQ277</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with draft status</field>
        </record>
        <record id="demo_authorization_delegation_278" model="bssic.request">
            <field name="name">Authorization Delegation - REQ278</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 6 days ago</field>
        </record>
        <record id="demo_authorization_delegation_279" model="bssic.request">
            <field name="name">Authorization Delegation - REQ279</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 55 days ago</field>
        </record>
        <record id="demo_authorization_delegation_280" model="bssic.request">
            <field name="name">Authorization Delegation - REQ280</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 56 days ago</field>
        </record>
        <record id="demo_authorization_delegation_281" model="bssic.request">
            <field name="name">Authorization Delegation - REQ281</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with direct_manager status</field>
        </record>
        <record id="demo_authorization_delegation_282" model="bssic.request">
            <field name="name">Authorization Delegation - REQ282</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_283" model="bssic.request">
            <field name="name">Authorization Delegation - REQ283</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_284" model="bssic.request">
            <field name="name">Authorization Delegation - REQ284</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_285" model="bssic.request">
            <field name="name">Authorization Delegation - REQ285</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_286" model="bssic.request">
            <field name="name">Authorization Delegation - REQ286</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_287" model="bssic.request">
            <field name="name">Authorization Delegation - REQ287</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_288" model="bssic.request">
            <field name="name">Authorization Delegation - REQ288</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 19 days ago</field>
        </record>
        <record id="demo_authorization_delegation_289" model="bssic.request">
            <field name="name">Authorization Delegation - REQ289</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with completed status</field>
        </record>
        <record id="demo_authorization_delegation_290" model="bssic.request">
            <field name="name">Authorization Delegation - REQ290</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with draft status</field>
        </record>
        <record id="demo_authorization_delegation_291" model="bssic.request">
            <field name="name">Authorization Delegation - REQ291</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_292" model="bssic.request">
            <field name="name">Authorization Delegation - REQ292</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with audit_manager status</field>
        </record>
        <record id="demo_authorization_delegation_293" model="bssic.request">
            <field name="name">Authorization Delegation - REQ293</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_294" model="bssic.request">
            <field name="name">Authorization Delegation - REQ294</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">500000.0</field>
            <field name="delegation_auth_limit">1000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_295" model="bssic.request">
            <field name="name">Authorization Delegation - REQ295</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 56 days ago</field>
        </record>
        <record id="demo_authorization_delegation_296" model="bssic.request">
            <field name="name">Authorization Delegation - REQ296</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 37 days ago</field>
        </record>
        <record id="demo_authorization_delegation_297" model="bssic.request">
            <field name="name">Authorization Delegation - REQ297</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with submitted status</field>
        </record>
        <record id="demo_authorization_delegation_298" model="bssic.request">
            <field name="name">Authorization Delegation - REQ298</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 38 days ago</field>
        </record>
        <record id="demo_authorization_delegation_299" model="bssic.request">
            <field name="name">Authorization Delegation - REQ299</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo data for Authorization Delegation workflow testing</field>
        </record>
        <record id="demo_authorization_delegation_300" model="bssic.request">
            <field name="name">Authorization Delegation - REQ300</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_301" model="bssic.request">
            <field name="name">Authorization Delegation - REQ301</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Sample Authorization Delegation with direct_manager status</field>
        </record>
        <record id="demo_authorization_delegation_302" model="bssic.request">
            <field name="name">Authorization Delegation - REQ302</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_303" model="bssic.request">
            <field name="name">Authorization Delegation - REQ303</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 32 days ago</field>
        </record>
        <record id="demo_authorization_delegation_304" model="bssic.request">
            <field name="name">Authorization Delegation - REQ304</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Test Authorization Delegation created 37 days ago</field>
        </record>
        <record id="demo_authorization_delegation_305" model="bssic.request">
            <field name="name">Authorization Delegation - REQ305</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=46)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Special project</field>
            <field name="delegation_details">Detailed explanation for special project</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_306" model="bssic.request">
            <field name="name">Authorization Delegation - REQ306</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="ceiling_reason">Temporary increase</field>
            <field name="delegation_details">Detailed explanation for temporary increase</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_307" model="bssic.request">
            <field name="name">Authorization Delegation - REQ307</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">100000.0</field>
            <field name="delegation_auth_limit">200000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=21)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_308" model="bssic.request">
            <field name="name">Authorization Delegation - REQ308</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">1000000.0</field>
            <field name="delegation_auth_limit">2000000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_309" model="bssic.request">
            <field name="name">Authorization Delegation - REQ309</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="ceiling_reason">Month-end processing</field>
            <field name="delegation_details">Detailed explanation for month-end processing</field>
            <field name="delegation_max_amount">750000.0</field>
            <field name="delegation_auth_limit">1500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        <record id="demo_authorization_delegation_310" model="bssic.request">
            <field name="name">Authorization Delegation - REQ310</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_authorization_delegation"/>
            <field name="request_type_code">authorization_delegation</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="ceiling_reason">Manager absence</field>
            <field name="delegation_details">Detailed explanation for manager absence</field>
            <field name="delegation_max_amount">250000.0</field>
            <field name="delegation_auth_limit">500000.0</field>
            <field name="delegation_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="delegation_to_date" eval="(DateTime.now() + timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Demo Authorization Delegation for testing purposes</field>
        </record>
        
        <!-- Free Form Requests -->
        <record id="demo_free_entry_311" model="bssic.request">
            <field name="name">Free Form - REQ311</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">ali.rashid</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with draft status</field>
        </record>
        <record id="demo_free_entry_312" model="bssic.request">
            <field name="name">Free Form - REQ312</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">rana.khalil</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_313" model="bssic.request">
            <field name="name">Free Form - REQ313</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">zeinab.karim</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_314" model="bssic.request">
            <field name="name">Free Form - REQ314</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">sami.habib</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_315" model="bssic.request">
            <field name="name">Free Form - REQ315</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">hassan.nasser</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_316" model="bssic.request">
            <field name="name">Free Form - REQ316</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">sami.habib</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_317" model="bssic.request">
            <field name="name">Free Form - REQ317</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">youssef.ahmad</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_318" model="bssic.request">
            <field name="name">Free Form - REQ318</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">rana.khalil</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_319" model="bssic.request">
            <field name="name">Free Form - REQ319</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">mohamed.saeed</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Sample Free Form with draft status</field>
        </record>
        <record id="demo_free_entry_320" model="bssic.request">
            <field name="name">Free Form - REQ320</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">khalid.mahmoud</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_321" model="bssic.request">
            <field name="name">Free Form - REQ321</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">zeinab.karim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_322" model="bssic.request">
            <field name="name">Free Form - REQ322</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=24)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">maryam.ibrahim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_323" model="bssic.request">
            <field name="name">Free Form - REQ323</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">mohamed.saeed</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_324" model="bssic.request">
            <field name="name">Free Form - REQ324</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Sample Free Form with completed status</field>
        </record>
        <record id="demo_free_entry_325" model="bssic.request">
            <field name="name">Free Form - REQ325</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">huda.mansour</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Sample Free Form with direct_manager status</field>
        </record>
        <record id="demo_free_entry_326" model="bssic.request">
            <field name="name">Free Form - REQ326</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">ali.rashid</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Test Free Form created 41 days ago</field>
        </record>
        <record id="demo_free_entry_327" model="bssic.request">
            <field name="name">Free Form - REQ327</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">amr.zaki</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_328" model="bssic.request">
            <field name="name">Free Form - REQ328</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">huda.mansour</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_329" model="bssic.request">
            <field name="name">Free Form - REQ329</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">khalid.mahmoud</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Test Free Form created 13 days ago</field>
        </record>
        <record id="demo_free_entry_330" model="bssic.request">
            <field name="name">Free Form - REQ330</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=59)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">zeinab.karim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_331" model="bssic.request">
            <field name="name">Free Form - REQ331</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">fatima.ali</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_332" model="bssic.request">
            <field name="name">Free Form - REQ332</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">hassan.nasser</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Test Free Form created 17 days ago</field>
        </record>
        <record id="demo_free_entry_333" model="bssic.request">
            <field name="name">Free Form - REQ333</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">aisha.farouk</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Sample Free Form with draft status</field>
        </record>
        <record id="demo_free_entry_334" model="bssic.request">
            <field name="name">Free Form - REQ334</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">omar.hassan</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Sample Free Form with submitted status</field>
        </record>
        <record id="demo_free_entry_335" model="bssic.request">
            <field name="name">Free Form - REQ335</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_336" model="bssic.request">
            <field name="name">Free Form - REQ336</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">aisha.farouk</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Test Free Form created 33 days ago</field>
        </record>
        <record id="demo_free_entry_337" model="bssic.request">
            <field name="name">Free Form - REQ337</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">ahmed.mohamed</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with submitted status</field>
        </record>
        <record id="demo_free_entry_338" model="bssic.request">
            <field name="name">Free Form - REQ338</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">maryam.ibrahim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with it_manager status</field>
        </record>
        <record id="demo_free_entry_339" model="bssic.request">
            <field name="name">Free Form - REQ339</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">dina.fouad</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_340" model="bssic.request">
            <field name="name">Free Form - REQ340</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">layla.saleh</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_341" model="bssic.request">
            <field name="name">Free Form - REQ341</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">amr.zaki</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with audit_manager status</field>
        </record>
        <record id="demo_free_entry_342" model="bssic.request">
            <field name="name">Free Form - REQ342</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">fatima.ali</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Test Free Form created 47 days ago</field>
        </record>
        <record id="demo_free_entry_343" model="bssic.request">
            <field name="name">Free Form - REQ343</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">maryam.ibrahim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_344" model="bssic.request">
            <field name="name">Free Form - REQ344</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">rana.khalil</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Test Free Form created 16 days ago</field>
        </record>
        <record id="demo_free_entry_345" model="bssic.request">
            <field name="name">Free Form - REQ345</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">zeinab.karim</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Sample Free Form with draft status</field>
        </record>
        <record id="demo_free_entry_346" model="bssic.request">
            <field name="name">Free Form - REQ346</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Test Free Form created 15 days ago</field>
        </record>
        <record id="demo_free_entry_347" model="bssic.request">
            <field name="name">Free Form - REQ347</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">amr.zaki</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_348" model="bssic.request">
            <field name="name">Free Form - REQ348</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">lina.badawi</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_349" model="bssic.request">
            <field name="name">Free Form - REQ349</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">dina.fouad</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_350" model="bssic.request">
            <field name="name">Free Form - REQ350</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Sample Free Form with direct_manager status</field>
        </record>
        <record id="demo_free_entry_351" model="bssic.request">
            <field name="name">Free Form - REQ351</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">fatima.ali</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Test Free Form created 19 days ago</field>
        </record>
        <record id="demo_free_entry_352" model="bssic.request">
            <field name="name">Free Form - REQ352</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">hassan.nasser</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Sample Free Form with audit_manager status</field>
        </record>
        <record id="demo_free_entry_353" model="bssic.request">
            <field name="name">Free Form - REQ353</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">dina.fouad</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Test Free Form created 28 days ago</field>
        </record>
        <record id="demo_free_entry_354" model="bssic.request">
            <field name="name">Free Form - REQ354</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">amr.zaki</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Test Free Form created 51 days ago</field>
        </record>
        <record id="demo_free_entry_355" model="bssic.request">
            <field name="name">Free Form - REQ355</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">lina.badawi</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Demo data for Free Form workflow testing</field>
        </record>
        <record id="demo_free_entry_356" model="bssic.request">
            <field name="name">Free Form - REQ356</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">hassan.nasser</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Test Free Form created 37 days ago</field>
        </record>
        <record id="demo_free_entry_357" model="bssic.request">
            <field name="name">Free Form - REQ357</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">layla.saleh</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Sample Free Form with in_progress status</field>
        </record>
        <record id="demo_free_entry_358" model="bssic.request">
            <field name="name">Free Form - REQ358</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with direct_manager status</field>
        </record>
        <record id="demo_free_entry_359" model="bssic.request">
            <field name="name">Free Form - REQ359</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">amr.zaki</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Sample Free Form with direct_manager status</field>
        </record>
        <record id="demo_free_entry_360" model="bssic.request">
            <field name="name">Free Form - REQ360</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">fatima.ali</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Test Free Form created 27 days ago</field>
        </record>
        <record id="demo_free_entry_361" model="bssic.request">
            <field name="name">Free Form - REQ361</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">ahmed.mohamed</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_362" model="bssic.request">
            <field name="name">Free Form - REQ362</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">mohamed.saeed</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_363" model="bssic.request">
            <field name="name">Free Form - REQ363</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">omar.hassan</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Sample Free Form with direct_manager status</field>
        </record>
        <record id="demo_free_entry_364" model="bssic.request">
            <field name="name">Free Form - REQ364</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">aisha.farouk</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Sample Free Form with it_manager status</field>
        </record>
        <record id="demo_free_entry_365" model="bssic.request">
            <field name="name">Free Form - REQ365</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">omar.hassan</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Test Free Form created 12 days ago</field>
        </record>
        <record id="demo_free_entry_366" model="bssic.request">
            <field name="name">Free Form - REQ366</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">layla.saleh</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Test Free Form created 25 days ago</field>
        </record>
        <record id="demo_free_entry_367" model="bssic.request">
            <field name="name">Free Form - REQ367</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">aisha.farouk</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Sample Free Form with assigned status</field>
        </record>
        <record id="demo_free_entry_368" model="bssic.request">
            <field name="name">Free Form - REQ368</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">dina.fouad</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Test Free Form created 48 days ago</field>
        </record>
        <record id="demo_free_entry_369" model="bssic.request">
            <field name="name">Free Form - REQ369</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="free_entry_subject">System maintenance</field>
            <field name="free_entry_user_name">zeinab.karim</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=9)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for system maintenance</field>
            <field name="description">Sample Free Form with draft status</field>
        </record>
        <record id="demo_free_entry_370" model="bssic.request">
            <field name="name">Free Form - REQ370</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="free_entry_subject">Emergency correction</field>
            <field name="free_entry_user_name">tariq.othman</field>
            <field name="free_entry_type">free_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for emergency correction</field>
            <field name="description">Test Free Form created 49 days ago</field>
        </record>
        <record id="demo_free_entry_371" model="bssic.request">
            <field name="name">Free Form - REQ371</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=51)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="free_entry_subject">Special access</field>
            <field name="free_entry_user_name">huda.mansour</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for special access</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        <record id="demo_free_entry_372" model="bssic.request">
            <field name="name">Free Form - REQ372</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_free_entry"/>
            <field name="request_type_code">free_entry</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="free_entry_subject">Audit investigation</field>
            <field name="free_entry_user_name">huda.mansour</field>
            <field name="free_entry_type">reverse_entry</field>
            <field name="free_entry_from_date" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_to_date" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="free_entry_details">Detailed explanation for audit investigation</field>
            <field name="description">Demo Free Form for testing purposes</field>
        </record>
        
        <!-- Permission Request Requests -->
        <record id="demo_permission_373" model="bssic.request">
            <field name="name">Permission Request - REQ373</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=78)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Sample Permission Request with submitted status</field>
        </record>
        <record id="demo_permission_374" model="bssic.request">
            <field name="name">Permission Request - REQ374</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">nour.abdullah</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_375" model="bssic.request">
            <field name="name">Permission Request - REQ375</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">add</field>
            <field name="user_name">sami.habib</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=83)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_1</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_376" model="bssic.request">
            <field name="name">Permission Request - REQ376</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="permission_type">activate</field>
            <field name="user_name">lina.badawi</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=81)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_1</field>
            <field name="description">Test Permission Request created 56 days ago</field>
        </record>
        <record id="demo_permission_377" model="bssic.request">
            <field name="name">Permission Request - REQ377</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">rana.khalil</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_378" model="bssic.request">
            <field name="name">Permission Request - REQ378</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">activate</field>
            <field name="user_name">ahmed.mohamed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=66)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_1</field>
            <field name="description">Test Permission Request created 4 days ago</field>
        </record>
        <record id="demo_permission_379" model="bssic.request">
            <field name="name">Permission Request - REQ379</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">youssef.ahmad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_2</field>
            <field name="description">Sample Permission Request with direct_manager status</field>
        </record>
        <record id="demo_permission_380" model="bssic.request">
            <field name="name">Permission Request - REQ380</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">dina.fouad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_3</field>
            <field name="description">Sample Permission Request with direct_manager status</field>
        </record>
        <record id="demo_permission_381" model="bssic.request">
            <field name="name">Permission Request - REQ381</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">ali.rashid</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=61)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_3</field>
            <field name="description">Test Permission Request created 47 days ago</field>
        </record>
        <record id="demo_permission_382" model="bssic.request">
            <field name="name">Permission Request - REQ382</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">maryam.ibrahim</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_383" model="bssic.request">
            <field name="name">Permission Request - REQ383</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=18)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">mohamed.saeed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=86)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_1</field>
            <field name="description">Test Permission Request created 18 days ago</field>
        </record>
        <record id="demo_permission_384" model="bssic.request">
            <field name="name">Permission Request - REQ384</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">hassan.nasser</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_385" model="bssic.request">
            <field name="name">Permission Request - REQ385</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="permission_type">activate</field>
            <field name="user_name">dina.fouad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_386" model="bssic.request">
            <field name="name">Permission Request - REQ386</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">mohamed.saeed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Test Permission Request created 2 days ago</field>
        </record>
        <record id="demo_permission_387" model="bssic.request">
            <field name="name">Permission Request - REQ387</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">activate</field>
            <field name="user_name">hassan.nasser</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=70)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Test Permission Request created 17 days ago</field>
        </record>
        <record id="demo_permission_388" model="bssic.request">
            <field name="name">Permission Request - REQ388</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">add</field>
            <field name="user_name">tariq.othman</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_389" model="bssic.request">
            <field name="name">Permission Request - REQ389</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">modify</field>
            <field name="user_name">maryam.ibrahim</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_390" model="bssic.request">
            <field name="name">Permission Request - REQ390</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">nour.abdullah</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=63)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_391" model="bssic.request">
            <field name="name">Permission Request - REQ391</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">ahmed.mohamed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_2</field>
            <field name="description">Sample Permission Request with in_progress status</field>
        </record>
        <record id="demo_permission_392" model="bssic.request">
            <field name="name">Permission Request - REQ392</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="permission_type">delete</field>
            <field name="user_name">zeinab.karim</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=73)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_393" model="bssic.request">
            <field name="name">Permission Request - REQ393</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">activate</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=67)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_1</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_394" model="bssic.request">
            <field name="name">Permission Request - REQ394</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="permission_type">modify</field>
            <field name="user_name">youssef.ahmad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=62)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_3</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_395" model="bssic.request">
            <field name="name">Permission Request - REQ395</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="permission_type">modify</field>
            <field name="user_name">sami.habib</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=88)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_396" model="bssic.request">
            <field name="name">Permission Request - REQ396</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="permission_type">delete</field>
            <field name="user_name">youssef.ahmad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Test Permission Request created 52 days ago</field>
        </record>
        <record id="demo_permission_397" model="bssic.request">
            <field name="name">Permission Request - REQ397</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">ahmed.mohamed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=67)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_3</field>
            <field name="description">Test Permission Request created 33 days ago</field>
        </record>
        <record id="demo_permission_398" model="bssic.request">
            <field name="name">Permission Request - REQ398</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=57)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">lina.badawi</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=75)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Sample Permission Request with direct_manager status</field>
        </record>
        <record id="demo_permission_399" model="bssic.request">
            <field name="name">Permission Request - REQ399</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">nour.abdullah</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_400" model="bssic.request">
            <field name="name">Permission Request - REQ400</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=50)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">add</field>
            <field name="user_name">aisha.farouk</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=76)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_401" model="bssic.request">
            <field name="name">Permission Request - REQ401</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">layla.saleh</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_1</field>
            <field name="description">Test Permission Request created 13 days ago</field>
        </record>
        <record id="demo_permission_402" model="bssic.request">
            <field name="name">Permission Request - REQ402</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">activate</field>
            <field name="user_name">huda.mansour</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Sample Permission Request with direct_manager status</field>
        </record>
        <record id="demo_permission_403" model="bssic.request">
            <field name="name">Permission Request - REQ403</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">activate</field>
            <field name="user_name">rana.khalil</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=74)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_1</field>
            <field name="description">Sample Permission Request with direct_manager status</field>
        </record>
        <record id="demo_permission_404" model="bssic.request">
            <field name="name">Permission Request - REQ404</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">omar.hassan</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_3</field>
            <field name="description">Test Permission Request created 17 days ago</field>
        </record>
        <record id="demo_permission_405" model="bssic.request">
            <field name="name">Permission Request - REQ405</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">tariq.othman</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_3</field>
            <field name="description">Test Permission Request created 55 days ago</field>
        </record>
        <record id="demo_permission_406" model="bssic.request">
            <field name="name">Permission Request - REQ406</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">activate</field>
            <field name="user_name">youssef.ahmad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=70)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_407" model="bssic.request">
            <field name="name">Permission Request - REQ407</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">nour.abdullah</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=83)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_1</field>
            <field name="description">Test Permission Request created 16 days ago</field>
        </record>
        <record id="demo_permission_408" model="bssic.request">
            <field name="name">Permission Request - REQ408</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">zeinab.karim</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=70)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_409" model="bssic.request">
            <field name="name">Permission Request - REQ409</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">modify</field>
            <field name="user_name">aisha.farouk</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=64)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_3</field>
            <field name="description">Sample Permission Request with submitted status</field>
        </record>
        <record id="demo_permission_410" model="bssic.request">
            <field name="name">Permission Request - REQ410</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">sami.habib</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=63)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_2</field>
            <field name="description">Test Permission Request created 17 days ago</field>
        </record>
        <record id="demo_permission_411" model="bssic.request">
            <field name="name">Permission Request - REQ411</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_412" model="bssic.request">
            <field name="name">Permission Request - REQ412</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=41)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">delete</field>
            <field name="user_name">dina.fouad</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=69)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_413" model="bssic.request">
            <field name="name">Permission Request - REQ413</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=73)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_414" model="bssic.request">
            <field name="name">Permission Request - REQ414</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">khalid.mahmoud</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_1</field>
            <field name="description">Test Permission Request created 36 days ago</field>
        </record>
        <record id="demo_permission_415" model="bssic.request">
            <field name="name">Permission Request - REQ415</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=26)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">omar.hassan</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=66)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_416" model="bssic.request">
            <field name="name">Permission Request - REQ416</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">delete</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=74)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_417" model="bssic.request">
            <field name="name">Permission Request - REQ417</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">sami.habib</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=84)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_2</field>
            <field name="description">Sample Permission Request with audit_manager status</field>
        </record>
        <record id="demo_permission_418" model="bssic.request">
            <field name="name">Permission Request - REQ418</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="permission_type">modify</field>
            <field name="user_name">huda.mansour</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=65)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_419" model="bssic.request">
            <field name="name">Permission Request - REQ419</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=53)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">ali.rashid</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_420" model="bssic.request">
            <field name="name">Permission Request - REQ420</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">ali.rashid</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_421" model="bssic.request">
            <field name="name">Permission Request - REQ421</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">lina.badawi</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=58)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Test Permission Request created 56 days ago</field>
        </record>
        <record id="demo_permission_422" model="bssic.request">
            <field name="name">Permission Request - REQ422</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="permission_type">delete</field>
            <field name="user_name">khalid.mahmoud</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_423" model="bssic.request">
            <field name="name">Permission Request - REQ423</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=49)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_424" model="bssic.request">
            <field name="name">Permission Request - REQ424</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=17)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">huda.mansour</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_425" model="bssic.request">
            <field name="name">Permission Request - REQ425</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">ali.rashid</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=35)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_426" model="bssic.request">
            <field name="name">Permission Request - REQ426</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">activate</field>
            <field name="user_name">hassan.nasser</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_427" model="bssic.request">
            <field name="name">Permission Request - REQ427</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">zeinab.karim</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_3</field>
            <field name="description">Test Permission Request created 39 days ago</field>
        </record>
        <record id="demo_permission_428" model="bssic.request">
            <field name="name">Permission Request - REQ428</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="permission_type">deactivate</field>
            <field name="user_name">ahmed.mohamed</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=31)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_429" model="bssic.request">
            <field name="name">Permission Request - REQ429</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="permission_type">add</field>
            <field name="user_name">layla.saleh</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_430" model="bssic.request">
            <field name="name">Permission Request - REQ430</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=34)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">aisha.farouk</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="forex_exchange" eval="True"/>
            <field name="forex_exchange">level_1</field>
            <field name="description">Demo data for Permission Request workflow testing</field>
        </record>
        <record id="demo_permission_431" model="bssic.request">
            <field name="name">Permission Request - REQ431</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=11)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="permission_type">add</field>
            <field name="user_name">rana.khalil</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=43)).strftime('%Y-%m-%d')"/>
            <field name="accounting_dept" eval="True"/>
            <field name="accounting_level">level_2</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_432" model="bssic.request">
            <field name="name">Permission Request - REQ432</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="permission_type">add</field>
            <field name="user_name">huda.mansour</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=63)).strftime('%Y-%m-%d')"/>
            <field name="back_office_deposits" eval="True"/>
            <field name="back_office_deposits">level_3</field>
            <field name="description">Test Permission Request created 38 days ago</field>
        </record>
        <record id="demo_permission_433" model="bssic.request">
            <field name="name">Permission Request - REQ433</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=23)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="permission_type">withdraw</field>
            <field name="user_name">sami.habib</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=81)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Demo Permission Request for testing purposes</field>
        </record>
        <record id="demo_permission_434" model="bssic.request">
            <field name="name">Permission Request - REQ434</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_permission"/>
            <field name="request_type_code">permission</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="permission_type">modify</field>
            <field name="user_name">amr.zaki</field>
            <field name="validity_from" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="validity_to" eval="(DateTime.now() + timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="risk_dept" eval="True"/>
            <field name="risk_level">level_3</field>
            <field name="description">Test Permission Request created 5 days ago</field>
        </record>
        
        <!-- Technical Request Requests -->
        <record id="demo_technical_435" model="bssic.request">
            <field name="name">Technical Request - REQ435</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_436" model="bssic.request">
            <field name="name">Technical Request - REQ436</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_437" model="bssic.request">
            <field name="name">Technical Request - REQ437</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 45 days ago</field>
        </record>
        <record id="demo_technical_438" model="bssic.request">
            <field name="name">Technical Request - REQ438</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=47)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Sample Technical Request with it_manager status</field>
        </record>
        <record id="demo_technical_439" model="bssic.request">
            <field name="name">Technical Request - REQ439</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_440" model="bssic.request">
            <field name="name">Technical Request - REQ440</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 44 days ago</field>
        </record>
        <record id="demo_technical_441" model="bssic.request">
            <field name="name">Technical Request - REQ441</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=29)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 29 days ago</field>
        </record>
        <record id="demo_technical_442" model="bssic.request">
            <field name="name">Technical Request - REQ442</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_443" model="bssic.request">
            <field name="name">Technical Request - REQ443</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_444" model="bssic.request">
            <field name="name">Technical Request - REQ444</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_445" model="bssic.request">
            <field name="name">Technical Request - REQ445</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 13 days ago</field>
        </record>
        <record id="demo_technical_446" model="bssic.request">
            <field name="name">Technical Request - REQ446</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=38)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Sample Technical Request with direct_manager status</field>
        </record>
        <record id="demo_technical_447" model="bssic.request">
            <field name="name">Technical Request - REQ447</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with direct_manager status</field>
        </record>
        <record id="demo_technical_448" model="bssic.request">
            <field name="name">Technical Request - REQ448</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_449" model="bssic.request">
            <field name="name">Technical Request - REQ449</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Sample Technical Request with completed status</field>
        </record>
        <record id="demo_technical_450" model="bssic.request">
            <field name="name">Technical Request - REQ450</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_451" model="bssic.request">
            <field name="name">Technical Request - REQ451</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_452" model="bssic.request">
            <field name="name">Technical Request - REQ452</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with submitted status</field>
        </record>
        <record id="demo_technical_453" model="bssic.request">
            <field name="name">Technical Request - REQ453</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=13)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Sample Technical Request with draft status</field>
        </record>
        <record id="demo_technical_454" model="bssic.request">
            <field name="name">Technical Request - REQ454</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_455" model="bssic.request">
            <field name="name">Technical Request - REQ455</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_456" model="bssic.request">
            <field name="name">Technical Request - REQ456</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Test Technical Request created 28 days ago</field>
        </record>
        <record id="demo_technical_457" model="bssic.request">
            <field name="name">Technical Request - REQ457</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">rejected</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with rejected status</field>
        </record>
        <record id="demo_technical_458" model="bssic.request">
            <field name="name">Technical Request - REQ458</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=44)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_459" model="bssic.request">
            <field name="name">Technical Request - REQ459</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_460" model="bssic.request">
            <field name="name">Technical Request - REQ460</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_461" model="bssic.request">
            <field name="name">Technical Request - REQ461</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_462" model="bssic.request">
            <field name="name">Technical Request - REQ462</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=56)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_463" model="bssic.request">
            <field name="name">Technical Request - REQ463</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=36)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 36 days ago</field>
        </record>
        <record id="demo_technical_464" model="bssic.request">
            <field name="name">Technical Request - REQ464</field>
            <field name="employee_id" ref="demo_employee_20"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 3 days ago</field>
        </record>
        <record id="demo_technical_465" model="bssic.request">
            <field name="name">Technical Request - REQ465</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Test Technical Request created 8 days ago</field>
        </record>
        <record id="demo_technical_466" model="bssic.request">
            <field name="name">Technical Request - REQ466</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=42)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_467" model="bssic.request">
            <field name="name">Technical Request - REQ467</field>
            <field name="employee_id" ref="demo_employee_6"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 5 days ago</field>
        </record>
        <record id="demo_technical_468" model="bssic.request">
            <field name="name">Technical Request - REQ468</field>
            <field name="employee_id" ref="demo_employee_11"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_469" model="bssic.request">
            <field name="name">Technical Request - REQ469</field>
            <field name="employee_id" ref="demo_employee_3"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_470" model="bssic.request">
            <field name="name">Technical Request - REQ470</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with completed status</field>
        </record>
        <record id="demo_technical_471" model="bssic.request">
            <field name="name">Technical Request - REQ471</field>
            <field name="employee_id" ref="demo_employee_8"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_472" model="bssic.request">
            <field name="name">Technical Request - REQ472</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Test Technical Request created 48 days ago</field>
        </record>
        <record id="demo_technical_473" model="bssic.request">
            <field name="name">Technical Request - REQ473</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 6 days ago</field>
        </record>
        <record id="demo_technical_474" model="bssic.request">
            <field name="name">Technical Request - REQ474</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 15 days ago</field>
        </record>
        <record id="demo_technical_475" model="bssic.request">
            <field name="name">Technical Request - REQ475</field>
            <field name="employee_id" ref="demo_employee_13"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_476" model="bssic.request">
            <field name="name">Technical Request - REQ476</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 28 days ago</field>
        </record>
        <record id="demo_technical_477" model="bssic.request">
            <field name="name">Technical Request - REQ477</field>
            <field name="employee_id" ref="demo_employee_17"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_478" model="bssic.request">
            <field name="name">Technical Request - REQ478</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Sample Technical Request with assigned status</field>
        </record>
        <record id="demo_technical_479" model="bssic.request">
            <field name="name">Technical Request - REQ479</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_480" model="bssic.request">
            <field name="name">Technical Request - REQ480</field>
            <field name="employee_id" ref="demo_employee_19"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Test Technical Request created 8 days ago</field>
        </record>
        <record id="demo_technical_481" model="bssic.request">
            <field name="name">Technical Request - REQ481</field>
            <field name="employee_id" ref="demo_employee_1"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_482" model="bssic.request">
            <field name="name">Technical Request - REQ482</field>
            <field name="employee_id" ref="demo_employee_15"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=28)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with direct_manager status</field>
        </record>
        <record id="demo_technical_483" model="bssic.request">
            <field name="name">Technical Request - REQ483</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=32)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 32 days ago</field>
        </record>
        <record id="demo_technical_484" model="bssic.request">
            <field name="name">Technical Request - REQ484</field>
            <field name="employee_id" ref="demo_employee_16"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=48)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_485" model="bssic.request">
            <field name="name">Technical Request - REQ485</field>
            <field name="employee_id" ref="demo_employee_5"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=54)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Sample Technical Request with in_progress status</field>
        </record>
        <record id="demo_technical_486" model="bssic.request">
            <field name="name">Technical Request - REQ486</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=55)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Test Technical Request created 55 days ago</field>
        </record>
        <record id="demo_technical_487" model="bssic.request">
            <field name="name">Technical Request - REQ487</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=52)).strftime('%Y-%m-%d')"/>
            <field name="state">completed</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_488" model="bssic.request">
            <field name="name">Technical Request - REQ488</field>
            <field name="employee_id" ref="demo_employee_10"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=19)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Test Technical Request created 19 days ago</field>
        </record>
        <record id="demo_technical_489" model="bssic.request">
            <field name="name">Technical Request - REQ489</field>
            <field name="employee_id" ref="demo_employee_9"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=39)).strftime('%Y-%m-%d')"/>
            <field name="state">submitted</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Sample Technical Request with submitted status</field>
        </record>
        <record id="demo_technical_490" model="bssic.request">
            <field name="name">Technical Request - REQ490</field>
            <field name="employee_id" ref="demo_employee_18"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=33)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_491" model="bssic.request">
            <field name="name">Technical Request - REQ491</field>
            <field name="employee_id" ref="demo_employee_2"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="state">assigned</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Sample Technical Request with assigned status</field>
        </record>
        <record id="demo_technical_492" model="bssic.request">
            <field name="name">Technical Request - REQ492</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">audit_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">2</field>
            <field name="description">Test Technical Request created 4 days ago</field>
        </record>
        <record id="demo_technical_493" model="bssic.request">
            <field name="name">Technical Request - REQ493</field>
            <field name="employee_id" ref="demo_employee_14"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=14)).strftime('%Y-%m-%d')"/>
            <field name="state">direct_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo data for Technical Request workflow testing</field>
        </record>
        <record id="demo_technical_494" model="bssic.request">
            <field name="name">Technical Request - REQ494</field>
            <field name="employee_id" ref="demo_employee_7"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=22)).strftime('%Y-%m-%d')"/>
            <field name="state">in_progress</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_495" model="bssic.request">
            <field name="name">Technical Request - REQ495</field>
            <field name="employee_id" ref="demo_employee_4"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=16)).strftime('%Y-%m-%d')"/>
            <field name="state">it_manager</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">1</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
        <record id="demo_technical_496" model="bssic.request">
            <field name="name">Technical Request - REQ496</field>
            <field name="employee_id" ref="demo_employee_12"/>
            <field name="request_type_id" ref="request_type_technical"/>
            <field name="request_type_code">technical</field>
            <field name="request_date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="state">draft</field>
            <field name="is_technical" eval="True"/>
            <field name="request_nature">technical</field>
            <field name="priority">3</field>
            <field name="description">Demo Technical Request for testing purposes</field>
        </record>
    
    </data>
</odoo>