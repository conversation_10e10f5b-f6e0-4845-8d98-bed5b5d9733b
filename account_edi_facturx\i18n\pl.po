# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_facturx
# 
# Translators:
# <PERSON><PERSON><PERSON> <sz<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2022-01-24 09:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "ConformanceLevel"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "DocumentFileName"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "DocumentType"
msgstr ""

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr "Wyświetlanie waluty"

#. module: account_edi_facturx
#: model:ir.model,name:account_edi_facturx.model_account_edi_format
msgid "EDI format"
msgstr "Format EDI"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Factur-X PDFA Extension Schema"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "INVOICE"
msgstr "FAKTURA"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "Faktura wygenerowana przez Odoo"

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it before trying again to import."
msgstr ""

#. module: account_edi_facturx
#: code:addons/account_edi_facturx/models/account_edi_format.py:0
#, python-format
msgid "No information about the journal or the type of invoice is passed"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Text"
msgstr "Tekst"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "The actual version of the Factur-X XML schema"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "The conformance level of the embedded Factur-X data"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "Version"
msgstr "Wersja"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "external"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "name of the embedded XML invoice file"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_facturx_export
msgid "urn:cen.eu:en16931:2017"
msgstr ""

#. module: account_edi_facturx
#: model_terms:ir.ui.view,arch_db:account_edi_facturx.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
