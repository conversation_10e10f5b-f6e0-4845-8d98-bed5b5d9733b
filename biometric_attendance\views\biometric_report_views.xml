<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Attendance Analysis View -->
    <record id="view_biometric_attendance_analysis" model="ir.ui.view">
        <field name="name">biometric.attendance.analysis</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <pivot string="Attendance Analysis">
                <field name="attendance_date" type="row" interval="month"/>
                <field name="employee_id" type="row"/>
                <field name="status" type="col"/>
                <field name="worked_hours" type="measure"/>
                <field name="late_minutes" type="measure"/>
                <field name="overtime_hours" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Attendance Analysis Action -->
    <record id="action_biometric_attendance_analysis" model="ir.actions.act_window">
        <field name="name">Attendance Analysis</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">pivot,graph</field>
        <field name="view_id" ref="view_biometric_attendance_analysis"/>
        <field name="context">{}</field>
    </record>

    <!-- Late Analysis Graph View -->
    <record id="view_biometric_late_analysis_graph" model="ir.ui.view">
        <field name="name">biometric.late.analysis.graph</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <graph string="Late Arrival Analysis" type="line">
                <field name="attendance_date" interval="day"/>
                <field name="late_minutes" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Late Analysis Action -->
    <record id="action_biometric_late_analysis" model="ir.actions.act_window">
        <field name="name">Late Arrival Analysis</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="view_biometric_late_analysis_graph"/>
        <field name="domain">[('status', '=', 'late')]</field>
        <field name="context">{}</field>
    </record>

    <!-- Absence Analysis Graph View -->
    <record id="view_biometric_absence_analysis_graph" model="ir.ui.view">
        <field name="name">biometric.absence.analysis.graph</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <graph string="Absence Analysis" type="bar">
                <field name="attendance_date" interval="week"/>
                <field name="employee_id" type="row"/>
            </graph>
        </field>
    </record>

    <!-- Absence Analysis Action -->
    <record id="action_biometric_absence_analysis" model="ir.actions.act_window">
        <field name="name">Absence Analysis</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="view_biometric_absence_analysis_graph"/>
        <field name="domain">[('status', '=', 'absent')]</field>
        <field name="context">{}</field>
    </record>

    <!-- Employee Performance Report -->
    <record id="view_biometric_employee_performance" model="ir.ui.view">
        <field name="name">biometric.employee.performance</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <pivot string="Employee Performance">
                <field name="department" type="row"/>
                <field name="name" type="row"/>
                <field name="total_attendance_days" type="measure"/>
                <field name="total_late_days" type="measure"/>
                <field name="total_absent_days" type="measure"/>
                <field name="average_work_hours" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Employee Performance Action -->
    <record id="action_biometric_employee_performance" model="ir.actions.act_window">
        <field name="name">Employee Performance</field>
        <field name="res_model">biometric.employee</field>
        <field name="view_mode">pivot,graph</field>
        <field name="view_id" ref="view_biometric_employee_performance"/>
        <field name="domain">[('active', '=', True)]</field>
    </record>

    <!-- Department Performance Report -->
    <record id="view_biometric_department_performance_graph" model="ir.ui.view">
        <field name="name">biometric.department.performance.graph</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <graph string="Department Performance" type="pie">
                <field name="employee_id" type="row"/>
                <field name="status" type="col"/>
            </graph>
        </field>
    </record>

    <!-- Department Performance Action -->
    <record id="action_biometric_department_performance" model="ir.actions.act_window">
        <field name="name">Department Performance</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="view_biometric_department_performance_graph"/>
        <field name="context">{}</field>
    </record>

    <!-- Report Menu Items -->
    <menuitem id="menu_biometric_report_attendance"
              name="Attendance Reports"
              parent="menu_biometric_reports"
              action="action_biometric_attendance_analysis"
              sequence="10"/>

    <menuitem id="menu_biometric_report_late"
              name="Late Arrival Reports"
              parent="menu_biometric_reports"
              action="action_biometric_late_analysis"
              sequence="20"/>

    <menuitem id="menu_biometric_report_absence"
              name="Absence Reports"
              parent="menu_biometric_reports"
              action="action_biometric_absence_analysis"
              sequence="30"/>

    <menuitem id="menu_biometric_attendance_analysis"
              name="Attendance Analysis"
              parent="menu_biometric_reports"
              action="action_biometric_attendance_analysis"
              sequence="40"/>

    <menuitem id="menu_biometric_late_analysis"
              name="Late Arrival Analysis"
              parent="menu_biometric_reports"
              action="action_biometric_late_analysis"
              sequence="50"/>

    <menuitem id="menu_biometric_absence_analysis"
              name="Absence Analysis"
              parent="menu_biometric_reports"
              action="action_biometric_absence_analysis"
              sequence="60"/>

    <menuitem id="menu_biometric_employee_performance"
              name="Employee Performance"
              parent="menu_biometric_reports"
              action="action_biometric_employee_performance"
              sequence="70"/>

    <menuitem id="menu_biometric_department_performance"
              name="Department Performance"
              parent="menu_biometric_reports"
              action="action_biometric_department_performance"
              sequence="80"/>
</odoo>
